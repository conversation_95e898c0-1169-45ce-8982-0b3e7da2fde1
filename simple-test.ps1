# Test simple de l'authentification
Write-Host "Test de connexion admin..." -ForegroundColor Yellow

$uri = "http://localhost:3001/auth/login"
$body = '{"email":"<EMAIL>","password":"Admin123!"}'
$headers = @{"Content-Type" = "application/json"}

$response = Invoke-WebRequest -Uri $uri -Method POST -Body $body -Headers $headers
$data = $response.Content | ConvertFrom-Json

Write-Host "Utilisateur: $($data.user.prenom) $($data.user.nom)" -ForegroundColor Green
Write-Host "Role: $($data.user.role)" -ForegroundColor Green
Write-Host "Token: $($data.token.Substring(0, 30))..." -ForegroundColor Green
