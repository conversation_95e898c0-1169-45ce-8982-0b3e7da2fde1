# Gestionnaire de permissions simple
Write-Host "GESTIONNAIRE DE PERMISSIONS" -ForegroundColor Yellow
Write-Host "============================" -ForegroundColor Yellow

# Connexion
$loginResponse = Invoke-RestMethod -Uri "http://localhost:3001/auth/login" -Method POST -Body '{"email":"<EMAIL>","password":"Admin123!"}' -ContentType "application/json"
$token = $loginResponse.token
$headers = @{ "Authorization" = "Bearer $token"; "Content-Type" = "application/json" }

Write-Host "Connecte en tant qu'administrateur" -ForegroundColor Green

# Recuperer utilisateurs
$usersResponse = Invoke-RestMethod -Uri "http://localhost:3001/admin/users" -Method GET -Headers $headers
$users = $usersResponse.users

Write-Host "`nUTILISATEURS:" -ForegroundColor Cyan
for ($i = 0; $i -lt $users.Count; $i++) {
    $user = $users[$i]
    Write-Host "[$($i+1)] $($user.prenom) $($user.nom) - $($user.email)" -ForegroundColor White
    Write-Host "     Creer: $($user.canCreateContacts) | Modifier: $($user.canEditContacts) | Supprimer: $($user.canDeleteContacts) | Voir tout: $($user.canViewAllContacts)" -ForegroundColor Gray
}

Write-Host "`nACTIONS RAPIDES:" -ForegroundColor Yellow
Write-Host "1. Donner tous les <NAME_EMAIL>" -ForegroundColor White
Write-Host "2. Retirer droit <NAME_EMAIL>" -ForegroundColor White
Write-Host "3. Donner droit voir <NAME_EMAIL>" -ForegroundColor White
Write-Host "4. Retirer tous <NAME_EMAIL>" -ForegroundColor White

$choice = Read-Host "`nVotre choix (1-4)"

switch ($choice) {
    "1" {
        $userTest = $users | Where-Object { $_.email -eq "<EMAIL>" }
        if ($userTest) {
            $permissionData = '{"canCreateContacts":true,"canEditContacts":true,"canDeleteContacts":true,"canViewAllContacts":false}'
            $updateResponse = Invoke-RestMethod -Uri "http://localhost:3001/admin/users/$($userTest.id)/permissions" -Method PUT -Body $permissionData -Headers $headers
            Write-Host "Tous les droits <NAME_EMAIL>" -ForegroundColor Green
        }
    }
    "2" {
        $userTest = $users | Where-Object { $_.email -eq "<EMAIL>" }
        if ($userTest) {
            $permissionData = '{"canCreateContacts":true,"canEditContacts":true,"canDeleteContacts":false,"canViewAllContacts":false}'
            $updateResponse = Invoke-RestMethod -Uri "http://localhost:3001/admin/users/$($userTest.id)/permissions" -Method PUT -Body $permissionData -Headers $headers
            Write-Host "Droit de suppression <NAME_EMAIL>" -ForegroundColor Green
        }
    }
    "3" {
        $manager = $users | Where-Object { $_.email -eq "<EMAIL>" }
        if ($manager) {
            $permissionData = '{"canCreateContacts":true,"canEditContacts":true,"canDeleteContacts":true,"canViewAllContacts":true}'
            $updateResponse = Invoke-RestMethod -Uri "http://localhost:3001/admin/users/$($manager.id)/permissions" -Method PUT -Body $permissionData -Headers $headers
            Write-Host "Droit voir tout <NAME_EMAIL>" -ForegroundColor Green
        }
    }
    "4" {
        $readonly = $users | Where-Object { $_.email -eq "<EMAIL>" }
        if ($readonly) {
            $permissionData = '{"canCreateContacts":false,"canEditContacts":false,"canDeleteContacts":false,"canViewAllContacts":false}'
            $updateResponse = Invoke-RestMethod -Uri "http://localhost:3001/admin/users/$($readonly.id)/permissions" -Method PUT -Body $permissionData -Headers $headers
            Write-Host "Tous les droits <NAME_EMAIL>" -ForegroundColor Green
        }
    }
}

Write-Host "`nPermissions mises a jour!" -ForegroundColor Green
Write-Host "Relancez le script pour voir les changements." -ForegroundColor Yellow
