const express = require('express');
const { body, validationResult } = require('express-validator');
const { PrismaClient } = require('@prisma/client');
const { authenticateToken, requireAdmin } = require('../middleware/auth');
const { getActivityHistory, getActivityStats } = require('../utils/logger');
const { hashPassword } = require('../utils/auth');

const router = express.Router();
const prisma = new PrismaClient();

// Middleware pour toutes les routes admin
router.use(authenticateToken);
router.use(requireAdmin);

/**
 * GET /admin/users - Récupérer tous les utilisateurs
 */
router.get('/users', async (req, res) => {
  try {
    const { page = 1, limit = 20, search, role, isActive } = req.query;
    const offset = (page - 1) * limit;

    const where = {};

    if (search) {
      where.OR = [
        { nom: { contains: search, mode: 'insensitive' } },
        { prenom: { contains: search, mode: 'insensitive' } },
        { email: { contains: search, mode: 'insensitive' } }
      ];
    }

    if (role) where.role = role;
    if (isActive !== undefined) where.isActive = isActive === 'true';

    const [users, total] = await Promise.all([
      prisma.user.findMany({
        where,
        select: {
          id: true,
          email: true,
          nom: true,
          prenom: true,
          role: true,
          isActive: true,
          lastLogin: true,
          createdAt: true
        },
        orderBy: { createdAt: 'desc' },
        take: parseInt(limit),
        skip: offset
      }),
      prisma.user.count({ where })
    ]);

    // Ajouter les compteurs pour chaque utilisateur
    const usersWithCounts = await Promise.all(
      users.map(async (user) => {
        const [contactCount, logCount] = await Promise.all([
          prisma.contact.count({ where: { userId: user.id } }),
          prisma.activityLog.count({ where: { userId: user.id } })
        ]);

        return {
          ...user,
          _count: {
            contacts: contactCount,
            activityLogs: logCount
          }
        };
      })
    );

    res.json({
      users: usersWithCounts,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Erreur lors de la récupération des utilisateurs:', error);
    res.status(500).json({ error: 'Erreur serveur lors de la récupération des utilisateurs' });
  }
});

/**
 * GET /admin/users/:id - Récupérer un utilisateur par ID
 */
router.get('/users/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const user = await prisma.user.findUnique({
      where: { id: parseInt(id) },
      select: {
        id: true,
        email: true,
        nom: true,
        prenom: true,
        role: true,
        isActive: true,
        lastLogin: true,
        createdAt: true,
        updatedAt: true
      }
    });

    if (!user) {
      return res.status(404).json({ error: 'Utilisateur non trouvé' });
    }

    // Ajouter les compteurs
    const [contactCount, logCount] = await Promise.all([
      prisma.contact.count({ where: { userId: user.id } }),
      prisma.activityLog.count({ where: { userId: user.id } })
    ]);

    user._count = {
      contacts: contactCount,
      activityLogs: logCount
    };

    res.json({ user });
  } catch (error) {
    console.error('Erreur lors de la récupération de l\'utilisateur:', error);
    res.status(500).json({ error: 'Erreur serveur lors de la récupération de l\'utilisateur' });
  }
});

/**
 * PUT /admin/users/:id - Mettre à jour un utilisateur
 */
router.put('/users/:id', [
  body('nom').optional().trim().isLength({ min: 2 }),
  body('prenom').optional().trim().isLength({ min: 2 }),
  body('email').optional().isEmail().normalizeEmail(),
  body('role').optional().isIn(['USER', 'ADMIN']),
  body('isActive').optional().isBoolean()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Données invalides',
        details: errors.array()
      });
    }

    const { id } = req.params;
    const { nom, prenom, email, role, isActive } = req.body;

    // Empêcher un admin de se désactiver lui-même
    if (parseInt(id) === req.user.id && isActive === false) {
      return res.status(400).json({
        error: 'Vous ne pouvez pas désactiver votre propre compte'
      });
    }

    const updateData = {};
    if (nom) updateData.nom = nom;
    if (prenom) updateData.prenom = prenom;
    if (email) updateData.email = email;
    if (role) updateData.role = role;
    if (isActive !== undefined) updateData.isActive = isActive;

    const updatedUser = await prisma.user.update({
      where: { id: parseInt(id) },
      data: updateData,
      select: {
        id: true,
        email: true,
        nom: true,
        prenom: true,
        role: true,
        isActive: true,
        updatedAt: true
      }
    });

    res.json({
      message: 'Utilisateur mis à jour avec succès',
      user: updatedUser
    });
  } catch (error) {
    console.error('Erreur lors de la mise à jour de l\'utilisateur:', error);

    if (error.code === 'P2025') {
      return res.status(404).json({ error: 'Utilisateur non trouvé' });
    }

    if (error.code === 'P2002' && error.meta?.target?.includes('email')) {
      return res.status(400).json({ error: 'Cet email est déjà utilisé' });
    }

    res.status(500).json({ error: 'Erreur serveur lors de la mise à jour de l\'utilisateur' });
  }
});

/**
 * DELETE /admin/users/:id - Supprimer un utilisateur
 */
router.delete('/users/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // Empêcher un admin de se supprimer lui-même
    if (parseInt(id) === req.user.id) {
      return res.status(400).json({
        error: 'Vous ne pouvez pas supprimer votre propre compte'
      });
    }

    await prisma.user.delete({
      where: { id: parseInt(id) }
    });

    res.status(204).send();
  } catch (error) {
    console.error('Erreur lors de la suppression de l\'utilisateur:', error);

    if (error.code === 'P2025') {
      return res.status(404).json({ error: 'Utilisateur non trouvé' });
    }

    res.status(500).json({ error: 'Erreur serveur lors de la suppression de l\'utilisateur' });
  }
});

/**
 * GET /admin/activity - Récupérer l'historique global des activités
 */
router.get('/activity', async (req, res) => {
  try {
    const {
      page = 1,
      limit = 50,
      userId,
      action,
      entityType,
      startDate,
      endDate
    } = req.query;

    const offset = (page - 1) * limit;

    const history = await getActivityHistory({
      userId: userId ? parseInt(userId) : undefined,
      action,
      entityType,
      startDate,
      endDate,
      limit: parseInt(limit),
      offset
    });

    // Compter le total pour la pagination
    const where = {};
    if (userId) where.userId = parseInt(userId);
    if (action) where.action = action;
    if (entityType) where.entityType = entityType;
    if (startDate || endDate) {
      where.createdAt = {};
      if (startDate) where.createdAt.gte = new Date(startDate);
      if (endDate) where.createdAt.lte = new Date(endDate);
    }

    const total = await prisma.activityLog.count({ where });

    // Parser les JSON strings
    const formattedHistory = history.map(log => ({
      ...log,
      oldValues: log.oldValues ? JSON.parse(log.oldValues) : null,
      newValues: log.newValues ? JSON.parse(log.newValues) : null
    }));

    res.json({
      activities: formattedHistory,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Erreur lors de la récupération de l\'historique:', error);
    res.status(500).json({ error: 'Erreur serveur lors de la récupération de l\'historique' });
  }
});

/**
 * GET /admin/stats - Récupérer les statistiques globales
 */
router.get('/stats', async (req, res) => {
  try {
    const [
      userStats,
      contactStats,
      activityStats,
      recentUsers,
      topActiveUsers
    ] = await Promise.all([
      // Statistiques des utilisateurs
      prisma.user.groupBy({
        by: ['role'],
        _count: { role: true }
      }),

      // Statistiques des contacts
      prisma.contact.count(),

      // Statistiques d'activité
      getActivityStats(),

      // Utilisateurs récents (7 derniers jours)
      prisma.user.count({
        where: {
          createdAt: {
            gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
          }
        }
      }),

      // Utilisateurs les plus actifs - récupérer d'abord les utilisateurs
      prisma.user.findMany({
        select: {
          id: true,
          nom: true,
          prenom: true,
          email: true
        },
        take: 10 // Prendre plus pour trier après
      })
    ]);

    // Calculer les utilisateurs les plus actifs
    const usersWithActivityCount = await Promise.all(
      topActiveUsers.map(async (user) => {
        const activityCount = await prisma.activityLog.count({
          where: { userId: user.id }
        });
        return {
          ...user,
          _count: {
            activityLogs: activityCount
          }
        };
      })
    );

    // Trier par nombre d'activités et prendre les 5 premiers
    const sortedActiveUsers = usersWithActivityCount
      .sort((a, b) => b._count.activityLogs - a._count.activityLogs)
      .slice(0, 5);

    res.json({
      users: {
        total: userStats.reduce((acc, stat) => acc + stat._count.role, 0),
        byRole: userStats.reduce((acc, stat) => {
          acc[stat.role] = stat._count.role;
          return acc;
        }, {}),
        recent: recentUsers
      },
      contacts: {
        total: contactStats
      },
      activity: activityStats,
      topActiveUsers: sortedActiveUsers
    });
  } catch (error) {
    console.error('Erreur lors de la récupération des statistiques:', error);
    res.status(500).json({ error: 'Erreur serveur lors de la récupération des statistiques' });
  }
});

/**
 * POST /admin/users/:id/reset-password - Réinitialiser le mot de passe d'un utilisateur
 */
router.post('/users/:id/reset-password', async (req, res) => {
  try {
    const { id } = req.params;
    const { newPassword } = req.body;

    if (!newPassword || newPassword.length < 8) {
      return res.status(400).json({
        error: 'Le nouveau mot de passe doit contenir au moins 8 caractères'
      });
    }

    const hashedPassword = await hashPassword(newPassword);

    await prisma.user.update({
      where: { id: parseInt(id) },
      data: { password: hashedPassword }
    });

    res.json({
      message: 'Mot de passe réinitialisé avec succès'
    });
  } catch (error) {
    console.error('Erreur lors de la réinitialisation du mot de passe:', error);

    if (error.code === 'P2025') {
      return res.status(404).json({ error: 'Utilisateur non trouvé' });
    }

    res.status(500).json({ error: 'Erreur serveur lors de la réinitialisation du mot de passe' });
  }
});

module.exports = router;
