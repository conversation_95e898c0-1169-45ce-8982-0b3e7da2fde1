const express = require('express');
const { body, validationResult } = require('express-validator');
const { PrismaClient } = require('@prisma/client');
const { authenticateToken, checkResourceOwnership } = require('../middleware/auth');
const {
  logContactCreate,
  logContactUpdate,
  logContactDelete
} = require('../utils/logger');

const router = express.Router();
const prisma = new PrismaClient();

// Validation rules pour les contacts
const contactValidation = [
  body('nom')
    .trim()
    .isLength({ min: 2 })
    .withMessage('Le nom doit contenir au moins 2 caractères'),
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Email invalide'),
  body('telephone')
    .trim()
    .isLength({ min: 10 })
    .withMessage('Le numéro de téléphone doit contenir au moins 10 caractères')
];

/**
 * GET /contacts - Récupérer tous les contacts de l'utilisateur connecté
 */
router.get('/', authenticateToken, async (req, res) => {
  try {
    const { search } = req.query;
    const userId = req.user.id;

    let contacts;
    if (search) {
      contacts = await prisma.contact.findMany({
        where: {
          userId,
          OR: [
            { nom: { contains: search, mode: 'insensitive' } },
            { email: { contains: search, mode: 'insensitive' } }
          ]
        },
        orderBy: { nom: 'asc' }
      });
    } else {
      contacts = await prisma.contact.findMany({
        where: { userId },
        orderBy: { nom: 'asc' }
      });
    }

    res.json(contacts);
  } catch (error) {
    console.error('Erreur lors de la récupération des contacts:', error);
    res.status(500).json({ error: 'Erreur serveur lors de la récupération des contacts' });
  }
});

/**
 * GET /contacts/:id - Récupérer un contact par ID
 */
router.get('/:id', authenticateToken, checkResourceOwnership('contact'), async (req, res) => {
  try {
    const { id } = req.params;
    const contact = await prisma.contact.findUnique({
      where: { id: parseInt(id) }
    });

    if (!contact) {
      return res.status(404).json({ error: 'Contact non trouvé' });
    }

    res.json(contact);
  } catch (error) {
    console.error('Erreur lors de la récupération du contact:', error);
    res.status(500).json({ error: 'Erreur serveur lors de la récupération du contact' });
  }
});

/**
 * POST /contacts - Créer un nouveau contact
 */
router.post('/', authenticateToken, contactValidation, async (req, res) => {
  try {
    // Vérifier les erreurs de validation
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Données invalides',
        details: errors.array()
      });
    }

    const { nom, email, telephone } = req.body;
    const userId = req.user.id;

    // Vérifier que l'email n'est pas déjà utilisé par cet utilisateur
    const existingContact = await prisma.contact.findFirst({
      where: {
        email,
        userId
      }
    });

    if (existingContact) {
      return res.status(400).json({ error: 'Un contact avec cet email existe déjà' });
    }

    const contact = await prisma.contact.create({
      data: { nom, email, telephone, userId }
    });

    // Logger la création
    await logContactCreate(userId, contact, req);

    res.status(201).json(contact);
  } catch (error) {
    console.error('Erreur lors de la création du contact:', error);

    // Gestion de l'erreur d'email unique globale
    if (error.code === 'P2002' && error.meta?.target?.includes('email')) {
      return res.status(400).json({ error: 'Cet email est déjà utilisé par un autre contact' });
    }

    res.status(500).json({ error: 'Erreur serveur lors de la création du contact' });
  }
});

/**
 * PUT /contacts/:id - Mettre à jour un contact
 */
router.put('/:id', authenticateToken, checkResourceOwnership('contact'), contactValidation, async (req, res) => {
  try {
    // Vérifier les erreurs de validation
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Données invalides',
        details: errors.array()
      });
    }

    const { id } = req.params;
    const { nom, email, telephone } = req.body;
    const userId = req.user.id;

    // Récupérer l'ancien contact pour le log
    const oldContact = await prisma.contact.findUnique({
      where: { id: parseInt(id) }
    });

    if (!oldContact) {
      return res.status(404).json({ error: 'Contact non trouvé' });
    }

    // Vérifier que l'email n'est pas déjà utilisé par un autre contact de cet utilisateur
    const existingContact = await prisma.contact.findFirst({
      where: {
        email,
        userId,
        id: { not: parseInt(id) }
      }
    });

    if (existingContact) {
      return res.status(400).json({ error: 'Un autre contact avec cet email existe déjà' });
    }

    const contact = await prisma.contact.update({
      where: { id: parseInt(id) },
      data: { nom, email, telephone }
    });

    // Logger la modification
    await logContactUpdate(userId, parseInt(id), oldContact, contact, req);

    res.json(contact);
  } catch (error) {
    console.error('Erreur lors de la mise à jour du contact:', error);

    // Gestion de l'erreur si le contact n'existe pas
    if (error.code === 'P2025') {
      return res.status(404).json({ error: 'Contact non trouvé' });
    }

    // Gestion de l'erreur d'email unique globale
    if (error.code === 'P2002' && error.meta?.target?.includes('email')) {
      return res.status(400).json({ error: 'Cet email est déjà utilisé par un autre contact' });
    }

    res.status(500).json({ error: 'Erreur serveur lors de la mise à jour du contact' });
  }
});

/**
 * DELETE /contacts/:id - Supprimer un contact
 */
router.delete('/:id', authenticateToken, checkResourceOwnership('contact'), async (req, res) => {
  console.log('🚀 DEBUT Route DELETE - Contact ID:', req.params.id, 'User ID:', req.user?.id);
  try {
    console.log('🔍 Route DELETE appelée pour contact ID:', req.params.id);
    const { id } = req.params;
    const userId = req.user.id;

    // Récupérer le contact pour le log avant suppression
    const contact = await prisma.contact.findUnique({
      where: { id: parseInt(id) }
    });

    console.log('🔍 Contact trouvé:', contact ? contact.nom : 'null');

    if (!contact) {
      console.log('❌ Contact non trouvé');
      return res.status(404).json({ error: 'Contact non trouvé' });
    }

    await prisma.contact.delete({
      where: { id: parseInt(id) }
    });

    // Logger la suppression
    console.log('🔍 Tentative de log de suppression pour contact:', contact.nom);
    try {
      await logContactDelete(userId, contact, req);
      console.log('✅ Log de suppression créé avec succès');
    } catch (logError) {
      console.error('❌ Erreur lors du log de suppression:', logError);
    }

    res.status(204).send();
  } catch (error) {
    console.error('Erreur lors de la suppression du contact:', error);

    // Gestion de l'erreur si le contact n'existe pas
    if (error.code === 'P2025') {
      return res.status(404).json({ error: 'Contact non trouvé' });
    }

    res.status(500).json({ error: 'Erreur serveur lors de la suppression du contact' });
  }
});

/**
 * GET /contacts/:id/history - Récupérer l'historique d'un contact
 */
router.get('/:id/history', authenticateToken, checkResourceOwnership('contact'), async (req, res) => {
  try {
    const { id } = req.params;

    const history = await prisma.activityLog.findMany({
      where: {
        contactId: parseInt(id),
        entityType: 'CONTACT'
      },
      include: {
        user: {
          select: {
            nom: true,
            prenom: true,
            email: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    // Parser les JSON strings pour oldValues et newValues
    const formattedHistory = history.map(log => ({
      ...log,
      oldValues: log.oldValues ? JSON.parse(log.oldValues) : null,
      newValues: log.newValues ? JSON.parse(log.newValues) : null
    }));

    res.json(formattedHistory);
  } catch (error) {
    console.error('Erreur lors de la récupération de l\'historique:', error);
    res.status(500).json({ error: 'Erreur serveur lors de la récupération de l\'historique' });
  }
});

module.exports = router;
