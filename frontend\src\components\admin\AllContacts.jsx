import React, { useState, useEffect } from 'react';
import { authenticatedAPI } from '../../services/authService';

const AllContacts = () => {
  const [contacts, setContacts] = useState([]);
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedUser, setSelectedUser] = useState('');

  useEffect(() => {
    fetchData();
  }, []);

  useEffect(() => {
    fetchContacts();
  }, [searchTerm, selectedUser]);

  const fetchData = async () => {
    try {
      setLoading(true);
      const [contactsResponse, usersResponse] = await Promise.all([
        authenticatedAPI.get('/admin/contacts'),
        authenticatedAPI.get('/admin/users')
      ]);
      
      setContacts(contactsResponse.data);
      setUsers(usersResponse.data.users || []);
    } catch (error) {
      console.error('Erreur lors de la récupération des données:', error);
      setError('Erreur lors de la récupération des données');
    } finally {
      setLoading(false);
    }
  };

  const fetchContacts = async () => {
    try {
      const params = new URLSearchParams();
      if (searchTerm) params.append('search', searchTerm);
      if (selectedUser) params.append('userId', selectedUser);
      
      const response = await authenticatedAPI.get(`/admin/contacts?${params}`);
      setContacts(response.data);
    } catch (error) {
      console.error('Erreur lors de la recherche:', error);
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('fr-FR', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getUserBadge = (user) => {
    const baseClasses = "px-2 py-1 rounded-full text-xs font-medium";
    if (user.role === 'ADMIN') {
      return <span className={`${baseClasses} bg-purple-100 text-purple-800`}>Admin</span>;
    }
    return <span className={`${baseClasses} bg-blue-100 text-blue-800`}>User</span>;
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2">Chargement des contacts...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-4">
        <p className="text-red-800">{error}</p>
        <button 
          onClick={fetchData}
          className="mt-2 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
        >
          Réessayer
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-gray-900">Tous les Contacts</h2>
        <div className="text-sm text-gray-600">
          {contacts.length} contact{contacts.length > 1 ? 's' : ''} trouvé{contacts.length > 1 ? 's' : ''}
        </div>
      </div>

      {/* Filtres */}
      <div className="bg-white p-4 rounded-lg shadow space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label htmlFor="search" className="block text-sm font-medium text-gray-700 mb-1">
              Rechercher
            </label>
            <input
              type="text"
              id="search"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="Nom, email..."
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          <div>
            <label htmlFor="user-filter" className="block text-sm font-medium text-gray-700 mb-1">
              Filtrer par utilisateur
            </label>
            <select
              id="user-filter"
              value={selectedUser}
              onChange={(e) => setSelectedUser(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">Tous les utilisateurs</option>
              {users.map((user) => (
                <option key={user.id} value={user.id}>
                  {user.prenom} {user.nom} ({user.email})
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* Liste des contacts */}
      <div className="bg-white shadow-lg rounded-lg overflow-hidden">
        {contacts.length === 0 ? (
          <div className="text-center py-8">
            <p className="text-gray-500">Aucun contact trouvé</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Contact
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Propriétaire
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Téléphone
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Créé le
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Modifié le
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {contacts.map((contact) => (
                  <tr key={contact.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900">
                          {contact.nom}
                        </div>
                        <div className="text-sm text-gray-500">{contact.email}</div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center space-x-2">
                        <div>
                          <div className="text-sm font-medium text-gray-900">
                            {contact.user.prenom} {contact.user.nom}
                          </div>
                          <div className="text-sm text-gray-500">{contact.user.email}</div>
                        </div>
                        {getUserBadge(contact.user)}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {contact.telephone}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {formatDate(contact.createdAt)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {contact.updatedAt !== contact.createdAt ? (
                        <span className="text-orange-600 font-medium">
                          {formatDate(contact.updatedAt)}
                        </span>
                      ) : (
                        <span className="text-gray-400">Non modifié</span>
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Statistiques */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h3 className="text-lg font-medium text-blue-900">Total Contacts</h3>
          <p className="text-2xl font-bold text-blue-600">{contacts.length}</p>
        </div>
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <h3 className="text-lg font-medium text-green-900">Utilisateurs Actifs</h3>
          <p className="text-2xl font-bold text-green-600">
            {new Set(contacts.map(c => c.user.id)).size}
          </p>
        </div>
        <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
          <h3 className="text-lg font-medium text-orange-900">Contacts Modifiés</h3>
          <p className="text-2xl font-bold text-orange-600">
            {contacts.filter(c => c.updatedAt !== c.createdAt).length}
          </p>
        </div>
      </div>
    </div>
  );
};

export default AllContacts;
