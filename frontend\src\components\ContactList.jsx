import React from 'react';
import ContactCard from './ContactCard';

const ContactList = ({ contacts, onEdit, onDelete, searchTerm, showModified, showNormal, totalContacts = contacts.length }) => {
  // Compter les contacts modifiés dans la liste filtrée
  const modifiedContacts = contacts.filter(contact =>
    contact.updatedAt && contact.createdAt &&
    new Date(contact.updatedAt).getTime() !== new Date(contact.createdAt).getTime()
  );

  // Déterminer si on affiche un sous-ensemble des contacts
  const isFiltered = searchTerm || !showModified || !showNormal;

  if (contacts.length === 0) {
    // Déterminer le message approprié selon les filtres actifs
    let message = 'Aucun contact trouvé';
    let description = '';

    if (searchTerm) {
      message = 'Aucun contact trouvé';
      description = `Aucun contact ne correspond à "${searchTerm}"`;
    } else if (!showModified && !showNormal) {
      message = 'Aucun filtre sélectionné';
      description = 'Veuillez cocher au moins un type de contact à afficher';
    } else if (!showModified) {
      message = 'Aucun contact normal trouvé';
      description = 'Tous vos contacts ont été modifiés';
    } else if (!showNormal) {
      message = 'Aucun contact modifié trouvé';
      description = 'Aucun contact n\'a encore été modifié';
    } else {
      message = 'Aucun contact';
      description = 'Commencez par ajouter votre premier contact';
    }

    return (
      <div className="text-center py-12">
        <div className="text-gray-400 mb-4">
          <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
          </svg>
        </div>
        <h3 className="text-xl font-medium text-gray-600 mb-2">
          {message}
        </h3>
        <p className="text-gray-500">
          {description}
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-3">
          <h2 className="text-2xl font-semibold text-gray-800">
            Contacts ({contacts.length}{isFiltered && totalContacts !== contacts.length ? ` sur ${totalContacts}` : ''})
          </h2>
          {modifiedContacts.length > 0 && (
            <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800 border border-blue-200">
              <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
              </svg>
              {modifiedContacts.length} modifié{modifiedContacts.length > 1 ? 's' : ''}
            </span>
          )}
        </div>
        <div className="flex items-center gap-3 text-sm text-gray-500">
          {searchTerm && (
            <span>Résultats pour "{searchTerm}"</span>
          )}
          {isFiltered && !searchTerm && (
            <span>
              {!showModified && !showNormal ? 'Aucun filtre actif' :
                !showModified ? 'Contacts normaux uniquement' :
                  !showNormal ? 'Contacts modifiés uniquement' : 'Tous les types'}
            </span>
          )}
        </div>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {contacts.map((contact) => (
          <ContactCard
            key={contact.id}
            contact={contact}
            onEdit={onEdit}
            onDelete={onDelete}
          />
        ))}
      </div>
    </div>
  );
};

export default ContactList;
