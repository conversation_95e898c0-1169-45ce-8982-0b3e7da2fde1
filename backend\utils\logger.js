const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

// Types d'actions
const ACTION_TYPES = {
  CREATE: 'CREATE',
  UPDATE: 'UPDATE',
  DELETE: 'DELETE',
  LOGIN: 'LOGIN',
  LOGOUT: 'LOGOUT',
  REGISTER: 'REGISTER'
};

// Types d'entités
const ENTITY_TYPES = {
  CONTACT: 'CONTACT',
  USER: 'USER',
  AUTH: 'AUTH'
};

/**
 * Enregistre une action dans les logs
 */
const logActivity = async ({
  userId,
  action,
  entityType,
  entityId = null,
  oldValues = null,
  newValues = null,
  description = null,
  req = null
}) => {
  try {
    // Extraire les informations de la requête si disponible
    let ipAddress = null;
    let userAgent = null;

    if (req) {
      ipAddress = req.ip || req.connection?.remoteAddress || req.headers?.['x-forwarded-for'];
      userAgent = req.headers?.['user-agent'] || (req.get ? req.get('User-Agent') : null);
    }

    // Créer l'entrée de log
    const logEntry = await prisma.activityLog.create({
      data: {
        userId,
        action,
        entityType,
        entityId,
        oldValues: oldValues ? JSON.stringify(oldValues) : null,
        newValues: newValues ? JSON.stringify(newValues) : null,
        ipAddress,
        userAgent,
        description,
        contactId: entityType === ENTITY_TYPES.CONTACT ? entityId : null
      }
    });

    console.log(`[LOG] ${action} ${entityType} by user ${userId}`, logEntry.id);
    return logEntry;
  } catch (error) {
    console.error('Erreur lors de l\'enregistrement du log:', error);
    // Ne pas faire échouer l'opération principale si le log échoue
  }
};

/**
 * Log de connexion utilisateur
 */
const logLogin = async (userId, req, success = true) => {
  return await logActivity({
    userId,
    action: ACTION_TYPES.LOGIN,
    entityType: ENTITY_TYPES.AUTH,
    description: success ? 'Connexion réussie' : 'Tentative de connexion échouée',
    req
  });
};

/**
 * Log de déconnexion utilisateur
 */
const logLogout = async (userId, req) => {
  return await logActivity({
    userId,
    action: ACTION_TYPES.LOGOUT,
    entityType: ENTITY_TYPES.AUTH,
    description: 'Déconnexion',
    req
  });
};

/**
 * Log d'inscription utilisateur
 */
const logRegister = async (userId, req) => {
  return await logActivity({
    userId,
    action: ACTION_TYPES.REGISTER,
    entityType: ENTITY_TYPES.USER,
    entityId: userId,
    description: 'Nouvel utilisateur enregistré',
    req
  });
};

/**
 * Log de création de contact
 */
const logContactCreate = async (userId, contact, req) => {
  return await logActivity({
    userId,
    action: ACTION_TYPES.CREATE,
    entityType: ENTITY_TYPES.CONTACT,
    entityId: contact.id,
    newValues: {
      nom: contact.nom,
      email: contact.email,
      telephone: contact.telephone
    },
    description: `Contact créé: ${contact.nom}`,
    req
  });
};

/**
 * Log de modification de contact
 */
const logContactUpdate = async (userId, contactId, oldContact, newContact, req) => {
  return await logActivity({
    userId,
    action: ACTION_TYPES.UPDATE,
    entityType: ENTITY_TYPES.CONTACT,
    entityId: contactId,
    oldValues: {
      nom: oldContact.nom,
      email: oldContact.email,
      telephone: oldContact.telephone
    },
    newValues: {
      nom: newContact.nom,
      email: newContact.email,
      telephone: newContact.telephone
    },
    description: `Contact modifié: ${newContact.nom}`,
    req
  });
};

/**
 * Log de suppression de contact
 */
const logContactDelete = async (userId, contact, req) => {
  return await logActivity({
    userId,
    action: ACTION_TYPES.DELETE,
    entityType: ENTITY_TYPES.CONTACT,
    entityId: contact.id,
    oldValues: {
      nom: contact.nom,
      email: contact.email,
      telephone: contact.telephone
    },
    description: `Contact supprimé: ${contact.nom}`,
    req
  });
};

/**
 * Récupère l'historique des activités
 */
const getActivityHistory = async (filters = {}) => {
  const {
    userId,
    action,
    entityType,
    startDate,
    endDate,
    limit = 100,
    offset = 0
  } = filters;

  const where = {};

  if (userId) where.userId = userId;
  if (action) where.action = action;
  if (entityType) where.entityType = entityType;
  if (startDate || endDate) {
    where.createdAt = {};
    if (startDate) where.createdAt.gte = new Date(startDate);
    if (endDate) where.createdAt.lte = new Date(endDate);
  }

  return await prisma.activityLog.findMany({
    where,
    include: {
      user: {
        select: {
          id: true,
          email: true,
          nom: true,
          prenom: true
        }
      },
      contact: {
        select: {
          id: true,
          nom: true,
          email: true
        }
      }
    },
    orderBy: {
      createdAt: 'desc'
    },
    take: limit,
    skip: offset
  });
};

/**
 * Statistiques d'activité
 */
const getActivityStats = async (userId = null) => {
  const where = userId ? { userId } : {};

  const [
    totalActions,
    actionsByType,
    recentActivity
  ] = await Promise.all([
    // Total des actions
    prisma.activityLog.count({ where }),

    // Actions par type
    prisma.activityLog.groupBy({
      by: ['action'],
      where,
      _count: {
        action: true
      }
    }),

    // Activité récente (7 derniers jours)
    prisma.activityLog.count({
      where: {
        ...where,
        createdAt: {
          gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
        }
      }
    })
  ]);

  return {
    totalActions,
    actionsByType: actionsByType.reduce((acc, item) => {
      acc[item.action] = item._count.action;
      return acc;
    }, {}),
    recentActivity
  };
};

module.exports = {
  ACTION_TYPES,
  ENTITY_TYPES,
  logActivity,
  logLogin,
  logLogout,
  logRegister,
  logContactCreate,
  logContactUpdate,
  logContactDelete,
  getActivityHistory,
  getActivityStats
};
