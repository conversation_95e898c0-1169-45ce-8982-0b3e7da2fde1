import axios from 'axios';

// Configuration de base d'Axios
const API_BASE_URL = 'http://localhost:3001';

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Service pour la gestion des contacts
export const contactsAPI = {
  // Récupérer tous les contacts avec recherche optionnelle
  getContacts: async (search = '') => {
    try {
      const params = search ? { search } : {};
      const response = await api.get('/contacts', { params });
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la récupération des contacts:', error);
      throw error;
    }
  },

  // Récupérer un contact par ID
  getContact: async (id) => {
    try {
      const response = await api.get(`/contacts/${id}`);
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la récupération du contact:', error);
      throw error;
    }
  },

  // Créer un nouveau contact
  createContact: async (contactData) => {
    try {
      const response = await api.post('/contacts', contactData);
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la création du contact:', error);
      throw error;
    }
  },

  // Mettre à jour un contact
  updateContact: async (id, contactData) => {
    try {
      const response = await api.put(`/contacts/${id}`, contactData);
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la mise à jour du contact:', error);
      throw error;
    }
  },

  // Supprimer un contact
  deleteContact: async (id) => {
    try {
      await api.delete(`/contacts/${id}`);
      return true;
    } catch (error) {
      console.error('Erreur lors de la suppression du contact:', error);
      throw error;
    }
  }
};

export default api;
