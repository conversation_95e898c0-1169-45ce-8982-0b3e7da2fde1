// Script pour tester les filtres de contacts modifiés/non modifiés
const axios = require('axios');

const API_BASE_URL = 'http://localhost:3001';

async function testFilters() {
  console.log('🧪 Test des filtres de contacts modifiés/non modifiés...\n');

  try {
    // 1. Récupérer tous les contacts
    console.log('1. État actuel des contacts...');
    const contactsResponse = await axios.get(`${API_BASE_URL}/contacts`);
    const contacts = contactsResponse.data;
    
    console.log(`📊 Total des contacts: ${contacts.length}`);
    
    // Analyser les contacts
    const modifiedContacts = contacts.filter(contact => 
      contact.updatedAt && contact.createdAt && 
      new Date(contact.updatedAt).getTime() !== new Date(contact.createdAt).getTime()
    );
    const normalContacts = contacts.filter(contact => 
      !contact.updatedAt || !contact.createdAt || 
      new Date(contact.updatedAt).getTime() === new Date(contact.createdAt).getTime()
    );

    console.log(`🔵 Contacts modifiés: ${modifiedContacts.length}`);
    console.log(`⚪ Contacts normaux: ${normalContacts.length}`);

    if (modifiedContacts.length > 0) {
      console.log('\n📝 Contacts modifiés:');
      modifiedContacts.forEach(contact => {
        console.log(`   - ${contact.nom} (modifié le ${new Date(contact.updatedAt).toLocaleString('fr-FR')})`);
      });
    }

    if (normalContacts.length > 0) {
      console.log('\n📄 Contacts normaux:');
      normalContacts.forEach(contact => {
        console.log(`   - ${contact.nom} (créé le ${new Date(contact.createdAt).toLocaleString('fr-FR')})`);
      });
    }

    // 2. Créer plus de contacts modifiés pour le test si nécessaire
    if (modifiedContacts.length < 3 && normalContacts.length > 0) {
      console.log('\n2. Création de plus de contacts modifiés pour le test...');
      
      for (let i = 0; i < Math.min(2, normalContacts.length); i++) {
        const contact = normalContacts[i];
        const updatedData = {
          nom: contact.nom,
          email: contact.email,
          telephone: contact.telephone + ' (test)'
        };
        
        await axios.put(`${API_BASE_URL}/contacts/${contact.id}`, updatedData);
        console.log(`   ✅ ${contact.nom} modifié`);
      }
    }

    // 3. État final
    console.log('\n3. État final après modifications...');
    const finalContactsResponse = await axios.get(`${API_BASE_URL}/contacts`);
    const finalContacts = finalContactsResponse.data;
    
    const finalModified = finalContacts.filter(contact => 
      contact.updatedAt && contact.createdAt && 
      new Date(contact.updatedAt).getTime() !== new Date(contact.createdAt).getTime()
    );
    const finalNormal = finalContacts.filter(contact => 
      !contact.updatedAt || !contact.createdAt || 
      new Date(contact.updatedAt).getTime() === new Date(contact.createdAt).getTime()
    );

    console.log(`📊 Total final: ${finalContacts.length} contacts`);
    console.log(`🔵 Modifiés: ${finalModified.length}`);
    console.log(`⚪ Normaux: ${finalNormal.length}`);

    console.log('\n🎯 Test des filtres dans l\'interface:');
    console.log('1. ✅ Cochez "Contacts modifiés" et décochez "Contacts normaux"');
    console.log(`   → Vous devriez voir ${finalModified.length} contact(s) en bleu`);
    console.log('2. ✅ Cochez "Contacts normaux" et décochez "Contacts modifiés"');
    console.log(`   → Vous devriez voir ${finalNormal.length} contact(s) en blanc`);
    console.log('3. ✅ Cochez les deux cases');
    console.log(`   → Vous devriez voir tous les ${finalContacts.length} contacts`);
    console.log('4. ❌ Décochez les deux cases');
    console.log('   → Vous devriez voir "Aucun filtre sélectionné"');

    console.log('\n🎉 Test terminé!');
    console.log('💡 Actualisez votre navigateur (F5) et testez les cases à cocher.');
    console.log('📱 Ouvrez http://localhost:3000 pour tester les filtres.');

  } catch (error) {
    console.error('\n❌ Erreur lors du test:', error.message);
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', error.response.data);
    }
    console.log('\n💡 Assurez-vous que le serveur backend est démarré sur le port 3001');
  }
}

// Vérifier si axios est disponible
try {
  require('axios');
  testFilters();
} catch (error) {
  console.log('❌ Axios n\'est pas installé.');
  console.log('Installez-le avec: npm install axios');
}
