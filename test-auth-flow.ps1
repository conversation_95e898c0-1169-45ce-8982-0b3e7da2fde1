# Script PowerShell pour tester le flux d'authentification complet
Write-Host "🔐 Test du flux d'authentification complet..." -ForegroundColor Yellow

# 1. Test de connexion admin
$loginUri = "http://localhost:3001/auth/login"
$loginBody = @{
    email = "<EMAIL>"
    password = "Admin123!"
} | ConvertTo-Json

$headers = @{
    "Content-Type" = "application/json"
}

try {
    Write-Host "`n1. Test de connexion admin..." -ForegroundColor Cyan
    $loginResponse = Invoke-WebRequest -Uri $loginUri -Method POST -Body $loginBody -Headers $headers
    $loginData = $loginResponse.Content | ConvertFrom-Json
    
    Write-Host "✅ Connexion réussie!" -ForegroundColor Green
    Write-Host "   Utilisateur: $($loginData.user.prenom) $($loginData.user.nom)" -ForegroundColor Green
    Write-Host "   Rôle: $($loginData.user.role)" -ForegroundColor Green
    Write-Host "   Token reçu: $($loginData.token.Substring(0, 20))..." -ForegroundColor Green
    
    $token = $loginData.token
    
    # 2. Test de récupération du profil avec le token
    Write-Host "`n2. Test de récupération du profil..." -ForegroundColor Cyan
    $authHeaders = @{
        "Content-Type" = "application/json"
        "Authorization" = "Bearer $token"
    }
    
    $profileResponse = Invoke-WebRequest -Uri "http://localhost:3001/auth/me" -Method GET -Headers $authHeaders
    $profileData = $profileResponse.Content | ConvertFrom-Json
    
    Write-Host "✅ Profil récupéré!" -ForegroundColor Green
    Write-Host "   Email: $($profileData.user.email)" -ForegroundColor Green
    Write-Host "   Rôle: $($profileData.user.role)" -ForegroundColor Green
    Write-Host "   Contacts: $($profileData.user._count.contacts)" -ForegroundColor Green
    
    # 3. Test d'accès aux contacts
    Write-Host "`n3. Test d'accès aux contacts..." -ForegroundColor Cyan
    $contactsResponse = Invoke-WebRequest -Uri "http://localhost:3001/contacts" -Method GET -Headers $authHeaders
    $contactsData = $contactsResponse.Content | ConvertFrom-Json
    
    Write-Host "✅ Contacts récupérés!" -ForegroundColor Green
    Write-Host "   Nombre de contacts: $($contactsData.Count)" -ForegroundColor Green
    
    # 4. Test d'accès admin (si c'est un admin)
    if ($loginData.user.role -eq "ADMIN") {
        Write-Host "`n4. Test d'accès administrateur..." -ForegroundColor Cyan
        $adminResponse = Invoke-WebRequest -Uri "http://localhost:3001/admin/users" -Method GET -Headers $authHeaders
        $adminData = $adminResponse.Content | ConvertFrom-Json
        
        Write-Host "✅ Accès admin confirmé!" -ForegroundColor Green
        Write-Host "   Nombre d'utilisateurs: $($adminData.users.Count)" -ForegroundColor Green
    }
    
    Write-Host "`n🎉 Tous les tests sont passés!" -ForegroundColor Green
    Write-Host "Le problème vient probablement du frontend." -ForegroundColor Yellow
    
} catch {
    Write-Host "❌ Erreur:" -ForegroundColor Red
    Write-Host $_.Exception.Message -ForegroundColor Red
    if ($_.Exception.Response) {
        try {
            $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
            $responseBody = $reader.ReadToEnd()
            Write-Host "Response: $responseBody" -ForegroundColor Red
        } catch {
            Write-Host "Impossible de lire la réponse d'erreur" -ForegroundColor Red
        }
    }
}
