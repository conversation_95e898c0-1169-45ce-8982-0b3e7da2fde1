# Test de suppression de contact avec journalisation
Write-Host "🧪 Test de suppression de contact avec journalisation..." -ForegroundColor Yellow

try {
    # 1. Connexion admin
    Write-Host "`n1. Connexion admin..." -ForegroundColor Cyan
    $loginResponse = Invoke-RestMethod -Uri "http://localhost:3001/auth/login" -Method POST -Body '{"email":"<EMAIL>","password":"Admin123!"}' -ContentType "application/json"
    $token = $loginResponse.token
    Write-Host "✅ Connecté en tant que: $($loginResponse.user.prenom) $($loginResponse.user.nom)" -ForegroundColor Green

    $headers = @{
        "Authorization" = "Bearer $token"
        "Content-Type" = "application/json"
    }

    # 2. Créer un contact de test
    Write-Host "`n2. Création d'un contact de test..." -ForegroundColor Cyan
    $contactData = @{
        nom = "Contact Test Suppression"
        email = "<EMAIL>"
        telephone = "0123456789"
    } | ConvertTo-Json

    $createResponse = Invoke-RestMethod -Uri "http://localhost:3001/contacts" -Method POST -Body $contactData -Headers $headers
    $contactId = $createResponse.id
    Write-Host "✅ Contact créé avec ID: $contactId" -ForegroundColor Green

    # 3. Vérifier que le contact existe
    Write-Host "`n3. Vérification du contact..." -ForegroundColor Cyan
    $getResponse = Invoke-RestMethod -Uri "http://localhost:3001/contacts/$contactId" -Method GET -Headers $headers
    Write-Host "✅ Contact trouvé: $($getResponse.nom)" -ForegroundColor Green

    # 4. Supprimer le contact
    Write-Host "`n4. Suppression du contact..." -ForegroundColor Cyan
    try {
        Invoke-RestMethod -Uri "http://localhost:3001/contacts/$contactId" -Method DELETE -Headers $headers
        Write-Host "✅ Contact supprimé avec succès!" -ForegroundColor Green
    } catch {
        Write-Host "❌ Erreur lors de la suppression: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host "Détails: $($_.Exception.Response)" -ForegroundColor Red
    }

    # 5. Vérifier que le contact n'existe plus
    Write-Host "`n5. Vérification de la suppression..." -ForegroundColor Cyan
    try {
        $checkResponse = Invoke-RestMethod -Uri "http://localhost:3001/contacts/$contactId" -Method GET -Headers $headers
        Write-Host "❌ Le contact existe encore!" -ForegroundColor Red
    } catch {
        Write-Host "✅ Contact bien supprimé (404 attendu)" -ForegroundColor Green
    }

    # 6. Vérifier les logs
    Write-Host "`n6. Vérification des logs..." -ForegroundColor Cyan
    $activityResponse = Invoke-RestMethod -Uri "http://localhost:3001/admin/activity?limit=5" -Method GET -Headers $headers
    
    Write-Host "📝 Dernières activités:" -ForegroundColor Yellow
    foreach ($activity in $activityResponse.activities) {
        $date = [DateTime]::Parse($activity.createdAt).ToString("dd/MM/yyyy HH:mm:ss")
        Write-Host "   [$($activity.action)] $($activity.entityType) - $($activity.description) ($date)" -ForegroundColor White
    }

} catch {
    Write-Host "❌ Erreur générale: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "Réponse: $responseBody" -ForegroundColor Red
    }
}
