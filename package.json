{"name": "contact-manager", "version": "1.0.0", "description": "Application web complète pour la gestion d'un carnet de contacts", "main": "index.js", "scripts": {"install:all": "npm install --prefix backend && npm install --prefix frontend", "dev": "concurrently \"npm run dev --prefix backend\" \"npm run dev --prefix frontend\"", "build": "npm run build --prefix frontend", "start:backend": "npm start --prefix backend", "start:frontend": "npm run preview --prefix frontend", "db:setup": "cd backend && npx prisma generate && npx prisma db push", "db:studio": "cd backend && npx prisma studio"}, "keywords": ["contacts", "react", "nodejs", "express", "prisma", "sqlite", "tailwindcss"], "author": "", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}, "workspaces": ["backend", "frontend"], "dependencies": {"axios": "^1.10.0"}}