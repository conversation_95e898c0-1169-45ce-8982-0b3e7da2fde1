# Test de la route de debug
$loginResponse = Invoke-RestMethod -Uri "http://localhost:3001/auth/login" -Method POST -Body '{"email":"<EMAIL>","password":"Admin123!"}' -ContentType "application/json"
$token = $loginResponse.token
$headers = @{ "Authorization" = "Bearer $token"; "Content-Type" = "application/json" }

# Creer contact
$contactData = '{"nom":"Test Route Debug","email":"<EMAIL>","telephone":"0123456789"}'
$createResponse = Invoke-RestMethod -Uri "http://localhost:3001/contacts" -Method POST -Body $contactData -Headers $headers
$contactId = $createResponse.id
Write-Host "Contact cree ID: $contactId"

# Supprimer avec la route de test
Write-Host "Test de la route de debug..."
Invoke-RestMethod -Uri "http://localhost:3001/contacts/test/$contactId" -Method DELETE -Headers $headers
Write-Host "Contact supprime via route de test"

# Verifier les logs
$activityResponse = Invoke-RestMethod -Uri "http://localhost:3001/admin/activity?limit=3" -Method GET -Headers $headers
Write-Host "Logs apres suppression:"
foreach ($activity in $activityResponse.activities) {
    Write-Host "  $($activity.action) - $($activity.description)"
}
