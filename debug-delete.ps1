# Test de debug pour la suppression
Write-Host "Debug de la suppression de contact..." -ForegroundColor Yellow

# Connexion
$loginResponse = Invoke-RestMethod -Uri "http://localhost:3001/auth/login" -Method POST -Body '{"email":"<EMAIL>","password":"Admin123!"}' -ContentType "application/json"
$token = $loginResponse.token
Write-Host "Connecte: $($loginResponse.user.prenom)" -ForegroundColor Green

$headers = @{
    "Authorization" = "Bearer $token"
    "Content-Type" = "application/json"
}

# Creer un contact
$contactData = '{"nom":"Debug Delete","email":"<EMAIL>","telephone":"0123456789"}'
$createResponse = Invoke-RestMethod -Uri "http://localhost:3001/contacts" -Method POST -Body $contactData -Headers $headers
$contactId = $createResponse.id
Write-Host "Contact cree ID: $contactId" -ForegroundColor Green

# Verifier que le contact existe
$getResponse = Invoke-RestMethod -Uri "http://localhost:3001/contacts/$contactId" -Method GET -Headers $headers
Write-Host "Contact trouve: $($getResponse.nom)" -ForegroundColor Green

# Supprimer avec debug
Write-Host "Suppression du contact ID $contactId..." -ForegroundColor Yellow
try {
    $deleteResponse = Invoke-WebRequest -Uri "http://localhost:3001/contacts/$contactId" -Method DELETE -Headers $headers
    Write-Host "Status: $($deleteResponse.StatusCode)" -ForegroundColor Green
    Write-Host "Response: $($deleteResponse.Content)" -ForegroundColor Green
} catch {
    Write-Host "Erreur DELETE: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "Response Body: $responseBody" -ForegroundColor Red
    }
}

# Verifier les logs immediatement apres
Write-Host "Verification immediate des logs..." -ForegroundColor Yellow
$activityResponse = Invoke-RestMethod -Uri "http://localhost:3001/admin/activity?limit=5" -Method GET -Headers $headers
foreach ($activity in $activityResponse.activities) {
    Write-Host "  $($activity.action) - $($activity.description)" -ForegroundColor White
}
