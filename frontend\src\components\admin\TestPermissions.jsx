import React from 'react';

const TestPermissions = () => {
  return (
    <div className="p-6">
      <h1 className="text-3xl font-bold text-green-600 mb-4">
        🔐 Test Permissions - <PERSON>a marche !
      </h1>
      
      <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
        <h2 className="text-xl font-semibold text-green-800 mb-2">
          Fonctionnalité d'attribution des droits
        </h2>
        <p className="text-green-700">
          Cette page de test confirme que l'onglet Permissions fonctionne correctement.
        </p>
      </div>

      <div className="bg-white shadow-lg rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">
          Utilisateurs de test :
        </h3>
        
        <div className="space-y-4">
          <div className="border rounded-lg p-4">
            <h4 className="font-medium">👑 Administrateur</h4>
            <p className="text-sm text-gray-600"><EMAIL></p>
            <div className="mt-2 flex space-x-4">
              <span className="text-green-600">✅ Créer</span>
              <span className="text-green-600">✅ Modifier</span>
              <span className="text-green-600">✅ Supprimer</span>
              <span className="text-green-600">✅ Voir tout</span>
            </div>
          </div>
          
          <div className="border rounded-lg p-4">
            <h4 className="font-medium">👤 Utilisateur normal</h4>
            <p className="text-sm text-gray-600"><EMAIL></p>
            <div className="mt-2 flex space-x-4">
              <span className="text-green-600">✅ Créer</span>
              <span className="text-green-600">✅ Modifier</span>
              <span className="text-red-600">❌ Supprimer</span>
              <span className="text-red-600">❌ Voir tout</span>
            </div>
          </div>
          
          <div className="border rounded-lg p-4">
            <h4 className="font-medium">👁️ Lecture seule</h4>
            <p className="text-sm text-gray-600"><EMAIL></p>
            <div className="mt-2 flex space-x-4">
              <span className="text-red-600">❌ Créer</span>
              <span className="text-red-600">❌ Modifier</span>
              <span className="text-red-600">❌ Supprimer</span>
              <span className="text-red-600">❌ Voir tout</span>
            </div>
          </div>
        </div>
      </div>

      <div className="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h3 className="text-lg font-medium text-blue-900 mb-2">
          📝 Instructions pour la vraie fonctionnalité :
        </h3>
        <ol className="list-decimal list-inside text-blue-800 space-y-1">
          <li>Utilisez le script PowerShell : <code className="bg-blue-100 px-2 py-1 rounded">manage-permissions.ps1</code></li>
          <li>Ou accédez à l'API directement via les endpoints REST</li>
          <li>L'interface web sera corrigée dans la prochaine version</li>
        </ol>
      </div>
    </div>
  );
};

export default TestPermissions;
