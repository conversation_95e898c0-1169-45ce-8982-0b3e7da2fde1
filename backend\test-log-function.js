const { PrismaClient } = require('@prisma/client');
const { logContactDelete } = require('./utils/logger');

const prisma = new PrismaClient();

async function testLogFunction() {
  console.log('🧪 Test de la fonction logContactDelete...\n');

  try {
    // Créer un contact de test
    const contact = await prisma.contact.create({
      data: {
        nom: 'Test Log Delete',
        email: '<EMAIL>',
        telephone: '0123456789',
        userId: 1 // Admin
      }
    });

    console.log('✅ Contact de test créé:', contact.nom);

    // Simuler une requête
    const mockReq = {
      ip: '127.0.0.1',
      get: (header) => {
        if (header === 'User-Agent') return 'Test-Agent';
        return null;
      }
    };

    // Tester la fonction de log
    console.log('🔍 Test de logContactDelete...');
    await logContactDelete(1, contact, mockReq);
    console.log('✅ logContactDelete exécuté sans erreur');

    // Vérifier que le log a été créé
    const logs = await prisma.activityLog.findMany({
      where: {
        contactId: contact.id,
        action: 'DELETE'
      },
      include: {
        user: {
          select: {
            prenom: true,
            nom: true
          }
        }
      }
    });

    if (logs.length > 0) {
      console.log('✅ Log DELETE trouvé dans la base de données:');
      logs.forEach(log => {
        console.log(`   - ${log.action}: ${log.description}`);
        console.log(`   - Utilisateur: ${log.user.prenom} ${log.user.nom}`);
        console.log(`   - Date: ${log.createdAt}`);
      });
    } else {
      console.log('❌ Aucun log DELETE trouvé dans la base de données');
    }

    // Nettoyer - supprimer le contact et les logs de test
    await prisma.activityLog.deleteMany({
      where: { contactId: contact.id }
    });
    await prisma.contact.delete({
      where: { id: contact.id }
    });

    console.log('🧹 Nettoyage terminé');

  } catch (error) {
    console.error('❌ Erreur lors du test:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testLogFunction();
