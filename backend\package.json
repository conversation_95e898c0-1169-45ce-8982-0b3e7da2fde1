{"name": "contact-manager-backend", "version": "1.0.0", "description": "Backend API pour la gestion de contacts", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio"}, "keywords": ["contacts", "api", "express", "prisma"], "author": "", "license": "ISC", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "@prisma/client": "^5.7.1"}, "devDependencies": {"nodemon": "^3.0.2", "prisma": "^5.7.1"}}