{"name": "contact-manager-backend", "version": "1.0.0", "description": "Backend API pour la gestion de contacts", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:seed": "node seed.js", "create-admin": "node create-admin.js", "create-admin:custom": "node create-admin.js --custom"}, "keywords": ["contacts", "api", "express", "prisma"], "author": "", "license": "ISC", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "@prisma/client": "^5.7.1", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "express-validator": "^7.0.1"}, "devDependencies": {"nodemon": "^3.0.2", "prisma": "^5.7.1"}}