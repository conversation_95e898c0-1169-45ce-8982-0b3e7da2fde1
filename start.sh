#!/bin/bash

# Script Bash pour démarrer l'application Contact Manager

echo "🚀 Démarrage de l'application Contact Manager..."

# Vérifier si Node.js est installé
if ! command -v node &> /dev/null; then
    echo "❌ Node.js n'est pas installé ou n'est pas dans le PATH"
    echo "Veuillez installer Node.js depuis https://nodejs.org/"
    exit 1
fi

NODE_VERSION=$(node --version)
echo "✅ Node.js détecté: $NODE_VERSION"

# Fonction pour installer les dépendances si nécessaire
install_dependencies() {
    local path=$1
    local name=$2
    
    if [ ! -d "$path/node_modules" ]; then
        echo "📦 Installation des dépendances pour $name..."
        cd $path
        npm install
        if [ $? -ne 0 ]; then
            echo "❌ Erreur lors de l'installation des dépendances pour $name"
            exit 1
        fi
        cd ..
    else
        echo "✅ Dépendances déjà installées pour $name"
    fi
}

# Installer les dépendances
install_dependencies "backend" "Backend"
install_dependencies "frontend" "Frontend"

# Configurer la base de données
echo "🗄️ Configuration de la base de données..."
cd backend

if [ ! -f ".env" ]; then
    cp "../.env.example" ".env"
    echo "✅ Fichier .env créé"
fi

# Générer le client Prisma et créer la base de données
npx prisma generate
npx prisma db push

cd ..

# Démarrer les serveurs
echo "🌐 Démarrage des serveurs..."

# Démarrer le backend en arrière-plan
echo "🔧 Démarrage du backend sur http://localhost:3001..."
cd backend
npm run dev &
BACKEND_PID=$!
cd ..

# Attendre un peu pour que le backend démarre
sleep 3

# Démarrer le frontend
echo "⚛️ Démarrage du frontend sur http://localhost:3000..."
cd frontend
npm run dev &
FRONTEND_PID=$!
cd ..

echo ""
echo "🎉 Application démarrée avec succès!"
echo "📱 Frontend: http://localhost:3000"
echo "🔧 Backend: http://localhost:3001"
echo ""
echo "Pour arrêter l'application, appuyez sur Ctrl+C"

# Attendre que l'utilisateur arrête l'application
trap "echo 'Arrêt de l\'application...'; kill $BACKEND_PID $FRONTEND_PID; exit" INT

# Garder le script en vie
wait
