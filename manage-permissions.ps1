# Script de gestion des permissions
Write-Host "=== GESTIONNAIRE DE PERMISSIONS ===" -ForegroundColor Yellow
Write-Host ""

# Connexion admin
Write-Host "Connexion administrateur..." -ForegroundColor Cyan
$loginResponse = Invoke-RestMethod -Uri "http://localhost:3001/auth/login" -Method POST -Body '{"email":"<EMAIL>","password":"Admin123!"}' -ContentType "application/json"
$token = $loginResponse.token
$headers = @{ "Authorization" = "Bearer $token"; "Content-Type" = "application/json" }

Write-Host "Connecte: $($loginResponse.user.prenom) $($loginResponse.user.nom)" -ForegroundColor Green
Write-Host ""

# Recuperer les utilisateurs
Write-Host "Recuperation des utilisateurs..." -ForegroundColor Cyan
$usersResponse = Invoke-RestMethod -Uri "http://localhost:3001/admin/users" -Method GET -Headers $headers
$users = $usersResponse.users

Write-Host "UTILISATEURS ACTUELS:" -ForegroundColor Yellow
Write-Host "=====================" -ForegroundColor Yellow
for ($i = 0; $i -lt $users.Count; $i++) {
    $user = $users[$i]
    Write-Host "[$($i+1)] $($user.prenom) $($user.nom) ($($user.email))" -ForegroundColor White
    Write-Host "     Role: $($user.role)" -ForegroundColor Gray
    Write-Host "     Permissions:" -ForegroundColor Gray
    Write-Host "       - Creer: $($user.canCreateContacts)" -ForegroundColor Gray
    Write-Host "       - Modifier: $($user.canEditContacts)" -ForegroundColor Gray
    Write-Host "       - Supprimer: $($user.canDeleteContacts)" -ForegroundColor Gray
    Write-Host "       - Voir tout: $($user.canViewAllContacts)" -ForegroundColor Gray
    Write-Host ""
}

# Menu interactif
do {
    Write-Host "QUE VOULEZ-VOUS FAIRE ?" -ForegroundColor Yellow
    Write-Host "1. Donner tous les droits a un utilisateur" -ForegroundColor White
    Write-Host "2. Retirer le droit de suppression a un utilisateur" -ForegroundColor White
    Write-Host "3. Permettre a un utilisateur de voir tous les contacts" -ForegroundColor White
    Write-Host "4. Retirer tous les droits de modification a un utilisateur" -ForegroundColor White
    Write-Host "5. Afficher les utilisateurs" -ForegroundColor White
    Write-Host "0. Quitter" -ForegroundColor White
    Write-Host ""
    
    $choice = Read-Host "Votre choix (0-5)"
    
    switch ($choice) {
        "1" {
            Write-Host "Utilisateurs disponibles:" -ForegroundColor Cyan
            for ($i = 0; $i -lt $users.Count; $i++) {
                Write-Host "[$($i+1)] $($users[$i].prenom) $($users[$i].nom)" -ForegroundColor White
            }
            $userChoice = Read-Host "Numero de l'utilisateur"
            if ($userChoice -match '^\d+$' -and [int]$userChoice -ge 1 -and [int]$userChoice -le $users.Count) {
                $selectedUser = $users[[int]$userChoice - 1]
                $permissionData = '{"canCreateContacts":true,"canEditContacts":true,"canDeleteContacts":true,"canViewAllContacts":false}'
                try {
                    $updateResponse = Invoke-RestMethod -Uri "http://localhost:3001/admin/users/$($selectedUser.id)/permissions" -Method PUT -Body $permissionData -Headers $headers
                    Write-Host "✅ Tous les droits accordes a $($selectedUser.prenom) $($selectedUser.nom)" -ForegroundColor Green
                } catch {
                    Write-Host "❌ Erreur: $($_.Exception.Message)" -ForegroundColor Red
                }
            }
        }
        "2" {
            Write-Host "Utilisateurs disponibles:" -ForegroundColor Cyan
            for ($i = 0; $i -lt $users.Count; $i++) {
                Write-Host "[$($i+1)] $($users[$i].prenom) $($users[$i].nom)" -ForegroundColor White
            }
            $userChoice = Read-Host "Numero de l'utilisateur"
            if ($userChoice -match '^\d+$' -and [int]$userChoice -ge 1 -and [int]$userChoice -le $users.Count) {
                $selectedUser = $users[[int]$userChoice - 1]
                $permissionData = '{"canCreateContacts":true,"canEditContacts":true,"canDeleteContacts":false,"canViewAllContacts":false}'
                try {
                    $updateResponse = Invoke-RestMethod -Uri "http://localhost:3001/admin/users/$($selectedUser.id)/permissions" -Method PUT -Body $permissionData -Headers $headers
                    Write-Host "✅ Droit de suppression retire pour $($selectedUser.prenom) $($selectedUser.nom)" -ForegroundColor Green
                } catch {
                    Write-Host "❌ Erreur: $($_.Exception.Message)" -ForegroundColor Red
                }
            }
        }
        "5" {
            # Recharger les utilisateurs
            $usersResponse = Invoke-RestMethod -Uri "http://localhost:3001/admin/users" -Method GET -Headers $headers
            $users = $usersResponse.users
            Write-Host "UTILISATEURS ACTUELS:" -ForegroundColor Yellow
            for ($i = 0; $i -lt $users.Count; $i++) {
                $user = $users[$i]
                Write-Host "[$($i+1)] $($user.prenom) $($user.nom) ($($user.email))" -ForegroundColor White
                Write-Host "     Permissions: C=$($user.canCreateContacts) M=$($user.canEditContacts) S=$($user.canDeleteContacts) V=$($user.canViewAllContacts)" -ForegroundColor Gray
            }
        }
    }
    Write-Host ""
} while ($choice -ne "0")

Write-Host "Au revoir!" -ForegroundColor Green
