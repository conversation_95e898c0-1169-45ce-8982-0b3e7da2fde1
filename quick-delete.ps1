# Test rapide de suppression
$loginResponse = Invoke-RestMethod -Uri "http://localhost:3001/auth/login" -Method POST -Body '{"email":"<EMAIL>","password":"Admin123!"}' -ContentType "application/json"
$token = $loginResponse.token
$headers = @{ "Authorization" = "Bearer $token"; "Content-Type" = "application/json" }

# Creer contact
$contactData = '{"nom":"Quick Delete","email":"<EMAIL>","telephone":"0123456789"}'
$createResponse = Invoke-RestMethod -Uri "http://localhost:3001/contacts" -Method POST -Body $contactData -Headers $headers
$contactId = $createResponse.id
Write-Host "Contact cree ID: $contactId"

# Supprimer
Invoke-RestMethod -Uri "http://localhost:3001/contacts/$contactId" -Method DELETE -Headers $headers
Write-Host "Contact supprime"
