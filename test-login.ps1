# Script PowerShell pour tester la connexion
$uri = "http://localhost:3001/auth/login"
$body = @{
    email = "<EMAIL>"
    password = "Admin123!"
} | ConvertTo-Json

$headers = @{
    "Content-Type" = "application/json"
}

try {
    Write-Host "Test de connexion admin..." -ForegroundColor Yellow
    $response = Invoke-WebRequest -Uri $uri -Method POST -Body $body -Headers $headers
    Write-Host "✅ Connexion réussie!" -ForegroundColor Green
    Write-Host "Status: $($response.StatusCode)" -ForegroundColor Green
    Write-Host "Response: $($response.Content)" -ForegroundColor Green
} catch {
    Write-Host "❌ Erreur de connexion:" -ForegroundColor Red
    Write-Host $_.Exception.Message -ForegroundColor Red
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "Response Body: $responseBody" -ForegroundColor Red
    }
}
