# Script simple pour demarrer l'application
Write-Host "Demarrage de l'application Contact Manager..." -ForegroundColor Green

# Demarrer le backend
Write-Host "Demarrage du backend..." -ForegroundColor Cyan
Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd backend; node server.js"

# Attendre 3 secondes
Start-Sleep -Seconds 3

# Demarrer le frontend
Write-Host "Demarrage du frontend..." -ForegroundColor Cyan
Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd frontend; npm run dev"

Write-Host "Application demarree!" -ForegroundColor Green
Write-Host "Frontend: http://localhost:3000" -ForegroundColor Cyan
Write-Host "Backend: http://localhost:3001" -ForegroundColor Cyan

# Attendre 5 secondes puis ouvrir le navigateur
Start-Sleep -Seconds 5
Start-Process "http://localhost:3000"
