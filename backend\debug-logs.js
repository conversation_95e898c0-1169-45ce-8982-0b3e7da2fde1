const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function debugLogs() {
  console.log('🔍 Vérification des logs dans la base de données...\n');

  try {
    // Récupérer tous les logs
    const logs = await prisma.activityLog.findMany({
      include: {
        user: {
          select: {
            id: true,
            email: true,
            prenom: true,
            nom: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: 20
    });

    console.log(`📊 Total des logs: ${logs.length}\n`);

    if (logs.length === 0) {
      console.log('❌ Aucun log trouvé dans la base de données!');
      console.log('Le système de journalisation ne fonctionne pas.\n');
    } else {
      console.log('📝 Derniers logs:');
      logs.forEach((log, index) => {
        console.log(`${index + 1}. [${log.action}] ${log.entityType || 'N/A'}`);
        console.log(`   Description: ${log.description}`);
        console.log(`   Utilisateur: ${log.user.prenom} ${log.user.nom} (${log.user.email})`);
        console.log(`   Date: ${new Date(log.createdAt).toLocaleString('fr-FR')}`);
        console.log(`   IP: ${log.ipAddress || 'N/A'}`);
        console.log('');
      });
    }

    // Vérifier les logs par type d'action
    const actionCounts = await prisma.activityLog.groupBy({
      by: ['action'],
      _count: {
        action: true
      }
    });

    console.log('📈 Répartition par type d\'action:');
    actionCounts.forEach(count => {
      console.log(`   ${count.action}: ${count._count.action}`);
    });

    // Vérifier les logs par utilisateur
    const userCounts = await prisma.activityLog.groupBy({
      by: ['userId'],
      _count: {
        userId: true
      }
    });

    console.log('\n👥 Répartition par utilisateur:');
    for (const count of userCounts) {
      const user = await prisma.user.findUnique({
        where: { id: count.userId },
        select: { prenom: true, nom: true, email: true }
      });
      console.log(`   ${user.prenom} ${user.nom} (${user.email}): ${count._count.userId} actions`);
    }

  } catch (error) {
    console.error('❌ Erreur lors de la vérification:', error);
  } finally {
    await prisma.$disconnect();
  }
}

debugLogs();
