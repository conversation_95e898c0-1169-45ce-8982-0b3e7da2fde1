import React, { useState, useEffect } from 'react';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import AuthPage from './components/auth/AuthPage';
import Navbar from './components/layout/Navbar';
import ContactList from './components/ContactList';
import ContactForm from './components/ContactForm';
import SearchBar from './components/SearchBar';
import AdminDashboard from './components/admin/AdminDashboard';
import { authenticatedContactsAPI } from './services/authService';

// Composant principal de l'application authentifiée
function ContactManager() {
  const { user, isAdmin } = useAuth();
  const [showAdmin, setShowAdmin] = useState(false);
  const [contacts, setContacts] = useState([]);
  const [filteredContacts, setFilteredContacts] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [editingContact, setEditingContact] = useState(null);
  const [showForm, setShowForm] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [showModified, setShowModified] = useState(true);
  const [showNormal, setShowNormal] = useState(true);
  const [permissions, setPermissions] = useState({
    canCreate: true,
    canEdit: true,
    canDelete: false,
    canViewAll: false
  });

  // Écouter les changements d'URL pour la navigation
  useEffect(() => {
    const handleHashChange = () => {
      const hash = window.location.hash.substring(1);
      setShowAdmin(hash === 'admin' && isAdmin());
    };

    window.addEventListener('hashchange', handleHashChange);
    handleHashChange();

    return () => {
      window.removeEventListener('hashchange', handleHashChange);
    };
  }, [isAdmin]);

  // Charger les contacts au démarrage
  useEffect(() => {
    if (!showAdmin) {
      fetchContacts();
    }
  }, [showAdmin]);

  // Filtrage des contacts
  useEffect(() => {
    let filtered = contacts.filter(contact =>
      contact.nom.toLowerCase().includes(searchTerm.toLowerCase()) ||
      contact.email.toLowerCase().includes(searchTerm.toLowerCase())
    );

    filtered = filtered.filter(contact => {
      const isModified = contact.updatedAt && contact.createdAt &&
        new Date(contact.updatedAt).getTime() !== new Date(contact.createdAt).getTime();

      if (isModified && !showModified) return false;
      if (!isModified && !showNormal) return false;
      return true;
    });

    setFilteredContacts(filtered);
  }, [contacts, searchTerm, showModified, showNormal]);

  const fetchContacts = async () => {
    try {
      setLoading(true);
      setError('');
      const response = await authenticatedContactsAPI.getContacts();
      setContacts(response.contacts || []);
      setPermissions(response.permissions || permissions);
    } catch (err) {
      console.error('Erreur lors du chargement des contacts:', err);
      setError('Erreur lors du chargement des contacts');
    } finally {
      setLoading(false);
    }
  };

  const handleAddContact = async (contactData) => {
    if (!permissions.canCreate) {
      setError('Vous n\'avez pas l\'autorisation de créer des contacts');
      return;
    }

    try {
      setError('');
      const newContact = await authenticatedContactsAPI.createContact(contactData);
      setContacts([...contacts, newContact]);
      setShowForm(false);
    } catch (err) {
      const errorMessage = err.response?.data?.error || 'Erreur lors de la création du contact';
      setError(errorMessage);
      throw err;
    }
  };

  const handleUpdateContact = async (contactData) => {
    if (!permissions.canEdit) {
      setError('Vous n\'avez pas l\'autorisation de modifier des contacts');
      return;
    }

    try {
      setError('');
      const updatedContact = await authenticatedContactsAPI.updateContact(editingContact.id, contactData);
      setContacts(contacts.map(contact =>
        contact.id === editingContact.id ? updatedContact : contact
      ));
      setEditingContact(null);
      setShowForm(false);
    } catch (err) {
      const errorMessage = err.response?.data?.error || 'Erreur lors de la modification du contact';
      setError(errorMessage);
      throw err;
    }
  };

  const handleDeleteContact = async (id) => {
    if (!permissions.canDelete) {
      setError('Vous n\'avez pas l\'autorisation de supprimer des contacts');
      return;
    }

    if (window.confirm('Êtes-vous sûr de vouloir supprimer ce contact ?')) {
      try {
        setError('');
        await authenticatedContactsAPI.deleteContact(id);
        setContacts(contacts.filter(contact => contact.id !== id));
      } catch (err) {
        const errorMessage = err.response?.data?.error || 'Erreur lors de la suppression du contact';
        setError(errorMessage);
      }
    }
  };

  const handleEditContact = (contact) => {
    setEditingContact(contact);
    setShowForm(true);
  };

  const handleCancelForm = () => {
    setEditingContact(null);
    setShowForm(false);
    setError('');
  };

  // Affichage conditionnel selon la page
  if (showAdmin) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navbar />
        <AdminDashboard />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      <div className="container mx-auto px-4 py-8">
        <header className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-800 mb-2">
            Mes Contacts
          </h1>
          <p className="text-gray-600">
            Bonjour {user?.prenom}, gérez facilement vos contacts
          </p>
        </header>

        {error && (
          <div className="mb-6 p-4 bg-red-100 border border-red-400 text-red-700 rounded-lg">
            {error}
          </div>
        )}

        <div className="mb-6 flex flex-col sm:flex-row gap-4 items-center justify-between">
          <SearchBar
            searchTerm={searchTerm}
            onSearchChange={setSearchTerm}
            placeholder="Rechercher par nom ou email..."
          />
          {permissions.canCreate && (
            <button
              onClick={() => setShowForm(true)}
              className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-medium transition-colors duration-200 flex items-center gap-2"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
              Ajouter un contact
            </button>
          )}
        </div>

        <div className="mb-4 p-4 bg-gray-50 rounded-lg border">
          <div className="flex flex-col sm:flex-row sm:items-center gap-4">
            <div className="flex items-center gap-4 text-sm">
              <span className="font-medium text-gray-700">Légende :</span>
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 bg-white border border-gray-200 rounded"></div>
                <span className="text-gray-600">Contact normal</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 bg-gradient-to-br from-blue-50 to-indigo-50 border-l-2 border-blue-500 rounded"></div>
                <span className="text-gray-600">Contact modifié</span>
              </div>
            </div>

            <div className="hidden sm:block w-px h-6 bg-gray-300"></div>

            <div className="flex items-center gap-4 text-sm">
              <span className="font-medium text-gray-700">Afficher :</span>
              <label className="flex items-center gap-2 cursor-pointer">
                <input
                  type="checkbox"
                  checked={showNormal}
                  onChange={(e) => setShowNormal(e.target.checked)}
                  className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"
                />
                <span className="text-gray-600">Contacts normaux</span>
              </label>
              <label className="flex items-center gap-2 cursor-pointer">
                <input
                  type="checkbox"
                  checked={showModified}
                  onChange={(e) => setShowModified(e.target.checked)}
                  className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"
                />
                <span className="text-gray-600">Contacts modifiés</span>
              </label>
            </div>
          </div>
        </div>

        {showForm && (
          <div className="mb-8">
            <ContactForm
              contact={editingContact}
              onSubmit={editingContact ? handleUpdateContact : handleAddContact}
              onCancel={handleCancelForm}
            />
          </div>
        )}

        {loading ? (
          <div className="text-center py-8">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <p className="mt-2 text-gray-600">Chargement des contacts...</p>
          </div>
        ) : (
          <ContactList
            contacts={filteredContacts}
            onEdit={permissions.canEdit ? handleEditContact : null}
            onDelete={permissions.canDelete ? handleDeleteContact : null}
            searchTerm={searchTerm}
            showModified={showModified}
            showNormal={showNormal}
            totalContacts={contacts.length}
            permissions={permissions}
          />
        )}
      </div>
    </div>
  );
}

// Composant qui gère l'affichage selon l'état d'authentification
function AppContent() {
  const { isAuthenticated, loading } = useAuth();

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Chargement...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return <AuthPage />;
  }

  return <ContactManager />;
}

// Composant App principal avec gestion de l'authentification
function App() {
  return (
    <AuthProvider>
      <AppContent />
    </AuthProvider>
  );
}

export default App;
