import axios from 'axios';

// Configuration de base d'Axios pour l'authentification
const API_BASE_URL = 'http://localhost:3001';

const authAPI = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Intercepteur pour ajouter automatiquement le token aux requêtes
authAPI.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('authToken');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Intercepteur pour gérer les erreurs d'authentification
authAPI.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Token expiré ou invalide
      localStorage.removeItem('authToken');
      localStorage.removeItem('user');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// Service d'authentification
export const authService = {
  // Connexion
  login: async (email, password) => {
    try {
      const response = await authAPI.post('/auth/login', { email, password });
      const { token, user } = response.data;
      
      // Stocker le token et les infos utilisateur
      localStorage.setItem('authToken', token);
      localStorage.setItem('user', JSON.stringify(user));
      
      return { token, user };
    } catch (error) {
      throw error;
    }
  },

  // Inscription
  register: async (userData) => {
    try {
      const response = await authAPI.post('/auth/register', userData);
      const { token, user } = response.data;
      
      // Stocker le token et les infos utilisateur
      localStorage.setItem('authToken', token);
      localStorage.setItem('user', JSON.stringify(user));
      
      return { token, user };
    } catch (error) {
      throw error;
    }
  },

  // Déconnexion
  logout: async () => {
    try {
      await authAPI.post('/auth/logout');
    } catch (error) {
      console.error('Erreur lors de la déconnexion:', error);
    } finally {
      // Nettoyer le stockage local même en cas d'erreur
      localStorage.removeItem('authToken');
      localStorage.removeItem('user');
    }
  },

  // Récupérer le profil utilisateur
  getProfile: async () => {
    try {
      const response = await authAPI.get('/auth/me');
      const user = response.data.user;
      
      // Mettre à jour les infos utilisateur stockées
      localStorage.setItem('user', JSON.stringify(user));
      
      return user;
    } catch (error) {
      throw error;
    }
  },

  // Mettre à jour le profil
  updateProfile: async (userData) => {
    try {
      const response = await authAPI.put('/auth/profile', userData);
      const user = response.data.user;
      
      // Mettre à jour les infos utilisateur stockées
      localStorage.setItem('user', JSON.stringify(user));
      
      return user;
    } catch (error) {
      throw error;
    }
  },

  // Vérifier si l'utilisateur est connecté
  isAuthenticated: () => {
    const token = localStorage.getItem('authToken');
    const user = localStorage.getItem('user');
    return !!(token && user);
  },

  // Récupérer l'utilisateur actuel depuis le stockage local
  getCurrentUser: () => {
    const userStr = localStorage.getItem('user');
    return userStr ? JSON.parse(userStr) : null;
  },

  // Récupérer le token actuel
  getToken: () => {
    return localStorage.getItem('authToken');
  },

  // Vérifier si l'utilisateur est admin
  isAdmin: () => {
    const user = authService.getCurrentUser();
    return user?.role === 'ADMIN';
  },

  // Vérifier si l'utilisateur a un rôle spécifique
  hasRole: (role) => {
    const user = authService.getCurrentUser();
    return user?.role === role || user?.role === 'ADMIN';
  }
};

// Service pour les requêtes authentifiées aux contacts
export const authenticatedContactsAPI = {
  // Récupérer tous les contacts de l'utilisateur
  getContacts: async (search = '') => {
    try {
      const params = search ? { search } : {};
      const response = await authAPI.get('/contacts', { params });
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  // Récupérer un contact par ID
  getContact: async (id) => {
    try {
      const response = await authAPI.get(`/contacts/${id}`);
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  // Créer un nouveau contact
  createContact: async (contactData) => {
    try {
      const response = await authAPI.post('/contacts', contactData);
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  // Mettre à jour un contact
  updateContact: async (id, contactData) => {
    try {
      const response = await authAPI.put(`/contacts/${id}`, contactData);
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  // Supprimer un contact
  deleteContact: async (id) => {
    try {
      await authAPI.delete(`/contacts/${id}`);
      return true;
    } catch (error) {
      throw error;
    }
  },

  // Récupérer l'historique d'un contact
  getContactHistory: async (id) => {
    try {
      const response = await authAPI.get(`/contacts/${id}/history`);
      return response.data;
    } catch (error) {
      throw error;
    }
  }
};

// Service d'administration (pour les admins)
export const adminAPI = {
  // Récupérer tous les utilisateurs
  getUsers: async (params = {}) => {
    try {
      const response = await authAPI.get('/admin/users', { params });
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  // Récupérer un utilisateur par ID
  getUser: async (id) => {
    try {
      const response = await authAPI.get(`/admin/users/${id}`);
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  // Mettre à jour un utilisateur
  updateUser: async (id, userData) => {
    try {
      const response = await authAPI.put(`/admin/users/${id}`, userData);
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  // Supprimer un utilisateur
  deleteUser: async (id) => {
    try {
      await authAPI.delete(`/admin/users/${id}`);
      return true;
    } catch (error) {
      throw error;
    }
  },

  // Récupérer l'historique global
  getActivity: async (params = {}) => {
    try {
      const response = await authAPI.get('/admin/activity', { params });
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  // Récupérer les statistiques
  getStats: async () => {
    try {
      const response = await authAPI.get('/admin/stats');
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  // Réinitialiser le mot de passe d'un utilisateur
  resetPassword: async (id, newPassword) => {
    try {
      const response = await authAPI.post(`/admin/users/${id}/reset-password`, {
        newPassword
      });
      return response.data;
    } catch (error) {
      throw error;
    }
  }
};

export default authAPI;
