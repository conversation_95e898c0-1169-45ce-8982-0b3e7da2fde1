/**
 * Middleware de vérification des permissions granulaires
 */

/**
 * Vérifie si l'utilisateur a la permission de créer des contacts
 */
const checkCreatePermission = (req, res, next) => {
  if (!req.user.canCreateContacts) {
    return res.status(403).json({ 
      error: 'Permission refusée',
      message: 'Vous n\'avez pas l\'autorisation de créer des contacts'
    });
  }
  next();
};

/**
 * Vérifie si l'utilisateur a la permission de modifier des contacts
 */
const checkEditPermission = (req, res, next) => {
  if (!req.user.canEditContacts) {
    return res.status(403).json({ 
      error: 'Permission refusée',
      message: 'Vous n\'avez pas l\'autorisation de modifier des contacts'
    });
  }
  next();
};

/**
 * Vérifie si l'utilisateur a la permission de supprimer des contacts
 */
const checkDeletePermission = (req, res, next) => {
  if (!req.user.canDeleteContacts) {
    return res.status(403).json({ 
      error: 'Permission refusée',
      message: 'Vous n\'avez pas l\'autorisation de supprimer des contacts'
    });
  }
  next();
};

/**
 * Vérifie si l'utilisateur peut voir tous les contacts ou seulement les siens
 */
const checkViewAllPermission = (req, res, next) => {
  // Ajouter une propriété pour indiquer si l'utilisateur peut voir tous les contacts
  req.canViewAll = req.user.canViewAllContacts || req.user.role === 'ADMIN';
  next();
};

/**
 * Vérifie si l'utilisateur a les droits d'administration
 */
const checkAdminPermission = (req, res, next) => {
  if (req.user.role !== 'ADMIN') {
    return res.status(403).json({ 
      error: 'Accès refusé',
      message: 'Seuls les administrateurs peuvent accéder à cette ressource'
    });
  }
  next();
};

/**
 * Middleware combiné pour vérifier la propriété ET les permissions
 */
const checkResourceOwnershipWithPermissions = (resourceType) => {
  return async (req, res, next) => {
    try {
      const { PrismaClient } = require('@prisma/client');
      const prisma = new PrismaClient();
      
      const resourceId = parseInt(req.params.id);
      const userId = req.user.id;
      
      let resource;
      
      if (resourceType === 'contact') {
        resource = await prisma.contact.findUnique({
          where: { id: resourceId }
        });
      }
      
      if (!resource) {
        return res.status(404).json({ error: `${resourceType} non trouvé` });
      }
      
      // Vérifier la propriété (sauf si l'utilisateur peut voir tous les contacts)
      const canViewAll = req.user.canViewAllContacts || req.user.role === 'ADMIN';
      if (!canViewAll && resource.userId !== userId) {
        return res.status(403).json({ 
          error: 'Accès refusé',
          message: `Vous ne pouvez accéder qu'à vos propres ${resourceType}s`
        });
      }
      
      // Ajouter la ressource à la requête pour éviter une nouvelle requête
      req.resource = resource;
      next();
      
    } catch (error) {
      console.error('Erreur lors de la vérification des permissions:', error);
      res.status(500).json({ error: 'Erreur interne du serveur' });
    }
  };
};

module.exports = {
  checkCreatePermission,
  checkEditPermission,
  checkDeletePermission,
  checkViewAllPermission,
  checkAdminPermission,
  checkResourceOwnershipWithPermissions
};
