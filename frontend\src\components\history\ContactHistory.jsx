import React, { useState, useEffect } from 'react';
import { authenticatedContactsAPI } from '../../services/authService';
import ActivityLog from './ActivityLog';

const ContactHistory = ({ contact, onClose }) => {
  const [history, setHistory] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    if (contact?.id) {
      loadContactHistory();
    }
  }, [contact?.id]);

  const loadContactHistory = async () => {
    try {
      setLoading(true);
      setError('');
      const data = await authenticatedContactsAPI.getContactHistory(contact.id);
      setHistory(data);
    } catch (err) {
      console.error('Erreur lors du chargement de l\'historique:', err);
      setError('Erreur lors du chargement de l\'historique du contact');
    } finally {
      setLoading(false);
    }
  };

  const exportContactHistory = (format) => {
    const timestamp = new Date().toISOString().split('T')[0];
    const filename = `historique_${contact.nom.replace(/\s+/g, '_')}_${timestamp}`;
    
    if (format === 'csv') {
      const headers = ['Date', 'Action', 'Description', 'Anciennes valeurs', 'Nouvelles valeurs'];
      const csvContent = [
        headers.join(','),
        ...history.map(item => [
          new Date(item.createdAt).toLocaleString('fr-FR'),
          item.action,
          `"${item.description.replace(/"/g, '""')}"`,
          `"${item.oldValues ? JSON.stringify(item.oldValues).replace(/"/g, '""') : ''}"`,
          `"${item.newValues ? JSON.stringify(item.newValues).replace(/"/g, '""') : ''}"`
        ].join(','))
      ].join('\n');

      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', `${filename}.csv`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } else if (format === 'json') {
      const exportData = {
        contact: {
          id: contact.id,
          nom: contact.nom,
          email: contact.email,
          telephone: contact.telephone
        },
        history: history,
        exportDate: new Date().toISOString()
      };
      
      const dataStr = JSON.stringify(exportData, null, 2);
      const blob = new Blob([dataStr], { type: 'application/json' });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', `${filename}.json`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  const getChangesSummary = (item) => {
    if (!item.oldValues || !item.newValues) return null;
    
    const changes = [];
    const oldVals = typeof item.oldValues === 'string' ? JSON.parse(item.oldValues) : item.oldValues;
    const newVals = typeof item.newValues === 'string' ? JSON.parse(item.newValues) : item.newValues;
    
    Object.keys(newVals).forEach(key => {
      if (oldVals[key] !== newVals[key]) {
        changes.push({
          field: key,
          old: oldVals[key],
          new: newVals[key]
        });
      }
    });
    
    return changes;
  };

  if (!contact) {
    return null;
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
        {/* En-tête */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div>
            <h2 className="text-xl font-semibold text-gray-800">
              Historique du contact
            </h2>
            <p className="text-sm text-gray-600 mt-1">
              {contact.nom} - {contact.email}
            </p>
          </div>
          
          <div className="flex items-center space-x-2">
            {/* Boutons d'export */}
            <button
              onClick={() => exportContactHistory('csv')}
              className="px-3 py-2 bg-green-600 text-white rounded-md text-sm hover:bg-green-700 transition-colors duration-200 flex items-center"
              disabled={loading || history.length === 0}
            >
              <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              CSV
            </button>
            
            <button
              onClick={() => exportContactHistory('json')}
              className="px-3 py-2 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700 transition-colors duration-200 flex items-center"
              disabled={loading || history.length === 0}
            >
              <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              JSON
            </button>
            
            <button
              onClick={onClose}
              className="p-2 text-gray-400 hover:text-gray-600 transition-colors duration-200"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        {/* Contenu */}
        <div className="overflow-y-auto max-h-[calc(90vh-120px)]">
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              <span className="ml-2 text-gray-600">Chargement de l'historique...</span>
            </div>
          ) : error ? (
            <div className="p-6">
              <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
                {error}
              </div>
            </div>
          ) : history.length === 0 ? (
            <div className="p-6 text-center">
              <svg className="w-12 h-12 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              <p className="text-gray-500">Aucun historique disponible pour ce contact</p>
            </div>
          ) : (
            <div className="p-6">
              <div className="space-y-4">
                {history.map((item, index) => {
                  const changes = getChangesSummary(item);
                  
                  return (
                    <div key={item.id || index} className="bg-gray-50 rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center space-x-2">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                            item.action === 'CREATE' ? 'bg-green-100 text-green-800' :
                            item.action === 'UPDATE' ? 'bg-blue-100 text-blue-800' :
                            item.action === 'DELETE' ? 'bg-red-100 text-red-800' :
                            'bg-gray-100 text-gray-800'
                          }`}>
                            {item.action === 'CREATE' ? 'Création' :
                             item.action === 'UPDATE' ? 'Modification' :
                             item.action === 'DELETE' ? 'Suppression' : item.action}
                          </span>
                        </div>
                        
                        <span className="text-sm text-gray-500">
                          {new Date(item.createdAt).toLocaleString('fr-FR')}
                        </span>
                      </div>
                      
                      <p className="text-sm text-gray-800 mb-2">
                        {item.description}
                      </p>
                      
                      {changes && changes.length > 0 && (
                        <div className="mt-3 p-3 bg-white rounded border">
                          <h4 className="text-xs font-semibold text-gray-700 mb-2">Modifications détaillées :</h4>
                          <div className="space-y-1">
                            {changes.map((change, changeIndex) => (
                              <div key={changeIndex} className="text-xs">
                                <span className="font-medium text-gray-600 capitalize">
                                  {change.field} :
                                </span>
                                <span className="text-red-600 line-through ml-1">
                                  {change.old || '(vide)'}
                                </span>
                                <span className="mx-1">→</span>
                                <span className="text-green-600">
                                  {change.new || '(vide)'}
                                </span>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                      
                      {item.ipAddress && (
                        <div className="mt-2 text-xs text-gray-500">
                          🌐 IP: {item.ipAddress}
                        </div>
                      )}
                    </div>
                  );
                })}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ContactHistory;
