const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkUsers() {
  console.log('🔍 Vérification des utilisateurs dans la base de données...\n');

  try {
    const users = await prisma.user.findMany({
      select: {
        id: true,
        email: true,
        nom: true,
        prenom: true,
        role: true,
        isActive: true,
        lastLogin: true,
        createdAt: true
      }
    });

    console.log(`📊 Total des utilisateurs: ${users.length}\n`);

    users.forEach((user, index) => {
      console.log(`${index + 1}. Utilisateur ID: ${user.id}`);
      console.log(`   Email: ${user.email}`);
      console.log(`   Nom: ${user.prenom} ${user.nom}`);
      console.log(`   Rôle: ${user.role}`);
      console.log(`   Actif: ${user.isActive ? 'Oui' : 'Non'}`);
      console.log(`   Dernière connexion: ${user.lastLogin ? new Date(user.lastLogin).toLocaleString('fr-FR') : 'Jamais'}`);
      console.log(`   Créé le: ${new Date(user.createdAt).toLocaleString('fr-FR')}`);
      console.log('');
    });

    // Vérifier spécifiquement l'admin
    const admin = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    });

    if (admin) {
      console.log('✅ Administrateur trouvé:');
      console.log(`   ID: ${admin.id}`);
      console.log(`   Email: ${admin.email}`);
      console.log(`   Rôle: ${admin.role}`);
      console.log(`   Actif: ${admin.isActive}`);
    } else {
      console.log('❌ Administrateur non trouvé!');
    }

    // Vérifier l'utilisateur test
    const testUser = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    });

    if (testUser) {
      console.log('\n✅ Utilisateur test trouvé:');
      console.log(`   ID: ${testUser.id}`);
      console.log(`   Email: ${testUser.email}`);
      console.log(`   Rôle: ${testUser.role}`);
      console.log(`   Actif: ${testUser.isActive}`);
    } else {
      console.log('\n❌ Utilisateur test non trouvé!');
    }

  } catch (error) {
    console.error('❌ Erreur lors de la vérification:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkUsers();
