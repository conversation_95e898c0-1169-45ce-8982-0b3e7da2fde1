const { PrismaClient } = require('@prisma/client');
const { hashPassword } = require('./utils/auth');
const { logRegister } = require('./utils/logger');

const prisma = new PrismaClient();

async function createAdmin() {
  console.log('🔧 Création du premier utilisateur administrateur...\n');

  try {
    // Vérifier s'il y a déjà un admin
    const existingAdmin = await prisma.user.findFirst({
      where: { role: 'ADMIN' }
    });

    if (existingAdmin) {
      console.log('✅ Un administrateur existe déjà:');
      console.log(`   Email: ${existingAdmin.email}`);
      console.log(`   Nom: ${existingAdmin.prenom} ${existingAdmin.nom}`);
      console.log(`   Créé le: ${existingAdmin.createdAt.toLocaleDateString('fr-FR')}`);
      return;
    }

    // Données par défaut pour l'admin
    const adminData = {
      email: '<EMAIL>',
      password: 'Admin123!',
      nom: 'Administrateur',
      prenom: 'Système',
      role: 'ADMIN'
    };

    console.log('📝 Création de l\'administrateur avec les données suivantes:');
    console.log(`   Email: ${adminData.email}`);
    console.log(`   Mot de passe: ${adminData.password}`);
    console.log(`   Nom: ${adminData.prenom} ${adminData.nom}`);
    console.log(`   Rôle: ${adminData.role}\n`);

    // Hacher le mot de passe
    const hashedPassword = await hashPassword(adminData.password);

    // Créer l'utilisateur admin
    const admin = await prisma.user.create({
      data: {
        email: adminData.email,
        password: hashedPassword,
        nom: adminData.nom,
        prenom: adminData.prenom,
        role: adminData.role
      }
    });

    // Logger la création (sans req car c'est un script)
    await logRegister(admin.id, null);

    console.log('🎉 Administrateur créé avec succès!');
    console.log(`   ID: ${admin.id}`);
    console.log(`   Email: ${admin.email}`);
    console.log(`   Créé le: ${admin.createdAt.toLocaleDateString('fr-FR')}\n`);

    console.log('🔐 Informations de connexion:');
    console.log(`   Email: ${adminData.email}`);
    console.log(`   Mot de passe: ${adminData.password}`);
    console.log('\n⚠️  IMPORTANT: Changez ce mot de passe après la première connexion!\n');

    console.log('🚀 Vous pouvez maintenant:');
    console.log('   1. Démarrer le serveur: npm run dev');
    console.log('   2. Vous connecter avec les identifiants ci-dessus');
    console.log('   3. Créer d\'autres utilisateurs depuis l\'interface admin');

  } catch (error) {
    console.error('❌ Erreur lors de la création de l\'administrateur:', error);
    
    if (error.code === 'P2002' && error.meta?.target?.includes('email')) {
      console.log('💡 Un utilisateur avec cet email existe déjà.');
    }
  } finally {
    await prisma.$disconnect();
  }
}

// Fonction pour créer un admin personnalisé
async function createCustomAdmin() {
  const readline = require('readline');
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });

  const question = (prompt) => new Promise((resolve) => rl.question(prompt, resolve));

  try {
    console.log('🔧 Création d\'un administrateur personnalisé...\n');

    const email = await question('Email: ');
    const password = await question('Mot de passe (min 8 caractères): ');
    const prenom = await question('Prénom: ');
    const nom = await question('Nom: ');

    // Validation basique
    if (!email || !password || !prenom || !nom) {
      console.log('❌ Tous les champs sont requis.');
      return;
    }

    if (password.length < 8) {
      console.log('❌ Le mot de passe doit contenir au moins 8 caractères.');
      return;
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      console.log('❌ Format d\'email invalide.');
      return;
    }

    // Hacher le mot de passe
    const hashedPassword = await hashPassword(password);

    // Créer l'utilisateur admin
    const admin = await prisma.user.create({
      data: {
        email,
        password: hashedPassword,
        nom,
        prenom,
        role: 'ADMIN'
      }
    });

    // Logger la création
    await logRegister(admin.id, null);

    console.log('\n🎉 Administrateur créé avec succès!');
    console.log(`   ID: ${admin.id}`);
    console.log(`   Email: ${admin.email}`);
    console.log(`   Nom: ${admin.prenom} ${admin.nom}`);

  } catch (error) {
    console.error('❌ Erreur lors de la création de l\'administrateur:', error);
    
    if (error.code === 'P2002' && error.meta?.target?.includes('email')) {
      console.log('💡 Un utilisateur avec cet email existe déjà.');
    }
  } finally {
    rl.close();
    await prisma.$disconnect();
  }
}

// Vérifier les arguments de ligne de commande
const args = process.argv.slice(2);

if (args.includes('--custom')) {
  createCustomAdmin();
} else {
  createAdmin();
}
