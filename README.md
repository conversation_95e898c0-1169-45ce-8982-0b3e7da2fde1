# Gestionnaire de Contacts

Une application web complète pour la gestion d'un carnet de contacts, développée avec React (frontend) et Node.js/Express (backend).

## 🚀 Fonctionnalités

- ✅ Affichage de la liste des contacts (nom, email, téléphone)
- ✅ Ajout d'un nouveau contact via un formulaire
- ✅ Modification d'un contact existant
- ✅ Suppression d'un contact avec confirmation
- ✅ Recherche en temps réel par nom ou email
- ✅ Design moderne et responsive avec Tailwind CSS
- ✅ Validation des données côté client et serveur
- ✅ Gestion des erreurs et messages informatifs

## 🛠️ Technologies utilisées

### Backend
- **Node.js** avec **Express.js**
- **Prisma ORM** pour la gestion de la base de données
- **SQLite** comme base de données
- **CORS** pour les requêtes cross-origin

### Frontend
- **React 18** avec **Vite**
- **Tailwind CSS** pour le styling
- **Axios** pour les requêtes HTTP
- **Design responsive** et moderne

## 📋 Prérequis

Avant de commencer, assurez-vous d'avoir installé :

- [Node.js](https://nodejs.org/) (version 16 ou supérieure)
- [npm](https://www.npmjs.com/) ou [yarn](https://yarnpkg.com/)

## 🚀 Installation et lancement

### 1. Cloner le projet

```bash
git clone <url-du-repo>
cd contact-manager
```

### 2. Configuration de l'environnement

Copiez le fichier `.env.example` vers `.env` dans le dossier backend :

```bash
cp .env.example backend/.env
```

### 3. Installation et lancement du backend

```bash
# Aller dans le dossier backend
cd backend

# Installer les dépendances
npm install

# Générer le client Prisma et créer la base de données
npx prisma generate
npx prisma db push

# Démarrer le serveur de développement
npm run dev
```

Le serveur backend sera accessible sur `http://localhost:3001`

### 4. Installation et lancement du frontend

Dans un nouveau terminal :

```bash
# Aller dans le dossier frontend
cd frontend

# Installer les dépendances
npm install

# Démarrer le serveur de développement
npm run dev
```

Le frontend sera accessible sur `http://localhost:3000`

## 📁 Structure du projet

```
contact-manager/
├── backend/                 # API Node.js + Express
│   ├── prisma/
│   │   └── schema.prisma   # Schéma de la base de données
│   ├── .env                # Variables d'environnement
│   ├── server.js           # Serveur Express principal
│   └── package.json
├── frontend/               # Application React
│   ├── src/
│   │   ├── components/     # Composants React
│   │   ├── services/       # Services API
│   │   ├── App.jsx         # Composant principal
│   │   └── main.jsx        # Point d'entrée
│   ├── index.html
│   ├── vite.config.js
│   ├── tailwind.config.js
│   └── package.json
├── .env.example            # Exemple de configuration
└── README.md
```

## 🔌 API Endpoints

### Contacts

- `GET /contacts` - Récupérer tous les contacts (avec recherche optionnelle)
- `GET /contacts/:id` - Récupérer un contact par ID
- `POST /contacts` - Créer un nouveau contact
- `PUT /contacts/:id` - Mettre à jour un contact
- `DELETE /contacts/:id` - Supprimer un contact

### Exemple de données

```json
{
  "id": 1,
  "nom": "Jean Dupont",
  "email": "<EMAIL>",
  "telephone": "0123456789",
  "createdAt": "2024-01-15T10:30:00.000Z",
  "updatedAt": "2024-01-15T10:30:00.000Z"
}
```

## 🧪 Scripts disponibles

### Backend

```bash
npm start          # Démarrer en production
npm run dev        # Démarrer en développement avec nodemon
npm run db:generate # Générer le client Prisma
npm run db:push    # Pousser le schéma vers la base de données
npm run db:migrate # Créer une migration
npm run db:studio  # Ouvrir Prisma Studio
```

### Frontend

```bash
npm run dev        # Démarrer le serveur de développement
npm run build      # Construire pour la production
npm run preview    # Prévisualiser la build de production
npm run lint       # Lancer ESLint
```

## 🎨 Fonctionnalités de l'interface

- **Liste des contacts** : Affichage en grille responsive avec informations essentielles
- **Recherche en temps réel** : Filtrage instantané par nom ou email
- **Formulaire d'ajout/modification** : Validation complète avec messages d'erreur
- **Actions rapides** : Boutons d'édition et suppression sur chaque contact
- **Confirmations** : Dialogue de confirmation pour la suppression
- **Messages d'état** : Indicateurs de chargement et messages d'erreur
- **Design responsive** : Optimisé pour mobile, tablette et desktop

## 🔧 Personnalisation

### Modifier les couleurs

Éditez le fichier `frontend/tailwind.config.js` pour personnaliser les couleurs :

```javascript
theme: {
  extend: {
    colors: {
      primary: '#your-color',
      secondary: '#your-color'
    }
  }
}
```

### Ajouter des champs au modèle Contact

1. Modifiez le schéma Prisma dans `backend/prisma/schema.prisma`
2. Exécutez `npx prisma db push`
3. Mettez à jour les composants React correspondants

## 🐛 Dépannage

### Problèmes courants

1. **Port déjà utilisé** : Changez le port dans les fichiers de configuration
2. **Base de données** : Supprimez `backend/prisma/dev.db` et relancez `npx prisma db push`
3. **CORS** : Vérifiez que le backend autorise les requêtes depuis le frontend

### Logs

- Backend : Les logs s'affichent dans le terminal où vous avez lancé `npm run dev`
- Frontend : Ouvrez les outils de développement du navigateur (F12)

## 📝 Licence

Ce projet est sous licence MIT. Voir le fichier `LICENSE` pour plus de détails.

## 🤝 Contribution

Les contributions sont les bienvenues ! N'hésitez pas à ouvrir une issue ou soumettre une pull request.

---

Développé avec ❤️ en utilisant React et Node.js
