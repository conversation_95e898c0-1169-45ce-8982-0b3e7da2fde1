import React, { useState, useEffect } from 'react';
import { authenticatedAPI } from '../../services/authService';

const UserPermissions = () => {
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [updating, setUpdating] = useState(null);

  useEffect(() => {
    fetchUsers();
  }, []);

  const fetchUsers = async () => {
    try {
      setLoading(true);
      const response = await authenticatedAPI.get('/admin/users');
      setUsers(response.data.users || []);
    } catch (error) {
      console.error('Erreur lors de la récupération des utilisateurs:', error);
      setError('Erreur lors de la récupération des utilisateurs');
    } finally {
      setLoading(false);
    }
  };

  const updateUserPermissions = async (userId, permissions) => {
    try {
      setUpdating(userId);
      const response = await authenticatedAPI.put(`/admin/users/${userId}/permissions`, permissions);
      
      // Mettre à jour l'utilisateur dans la liste
      setUsers(users.map(user => 
        user.id === userId ? { ...user, ...response.data.user } : user
      ));
      
      // Afficher un message de succès (vous pouvez ajouter un toast ici)
      console.log('Permissions mises à jour avec succès');
    } catch (error) {
      console.error('Erreur lors de la mise à jour des permissions:', error);
      setError('Erreur lors de la mise à jour des permissions');
    } finally {
      setUpdating(null);
    }
  };

  const handlePermissionChange = (userId, permission, value) => {
    const user = users.find(u => u.id === userId);
    if (!user) return;

    const newPermissions = {
      canCreateContacts: user.canCreateContacts,
      canEditContacts: user.canEditContacts,
      canDeleteContacts: user.canDeleteContacts,
      canViewAllContacts: user.canViewAllContacts,
      isActive: user.isActive,
      [permission]: value
    };

    updateUserPermissions(userId, newPermissions);
  };

  const getPermissionIcon = (hasPermission) => {
    return hasPermission ? (
      <span className="text-green-600">✓</span>
    ) : (
      <span className="text-red-600">✗</span>
    );
  };

  const getRoleBadge = (role) => {
    const baseClasses = "px-2 py-1 rounded-full text-xs font-medium";
    if (role === 'ADMIN') {
      return <span className={`${baseClasses} bg-purple-100 text-purple-800`}>Admin</span>;
    }
    return <span className={`${baseClasses} bg-blue-100 text-blue-800`}>Utilisateur</span>;
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2">Chargement des utilisateurs...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-4">
        <p className="text-red-800">{error}</p>
        <button 
          onClick={fetchUsers}
          className="mt-2 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
        >
          Réessayer
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-gray-900">Gestion des Permissions</h2>
        <button 
          onClick={fetchUsers}
          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          Actualiser
        </button>
      </div>

      <div className="bg-white shadow-lg rounded-lg overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Utilisateur
                </th>
                <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Rôle
                </th>
                <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Créer
                </th>
                <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Modifier
                </th>
                <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Supprimer
                </th>
                <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Voir Tout
                </th>
                <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actif
                </th>
                <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Contacts
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {users.map((user) => (
                <tr key={user.id} className={updating === user.id ? 'opacity-50' : ''}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div>
                        <div className="text-sm font-medium text-gray-900">
                          {user.prenom} {user.nom}
                        </div>
                        <div className="text-sm text-gray-500">{user.email}</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-center">
                    {getRoleBadge(user.role)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-center">
                    {user.role === 'ADMIN' && user.email === '<EMAIL>' ? (
                      getPermissionIcon(user.canCreateContacts)
                    ) : (
                      <input
                        type="checkbox"
                        checked={user.canCreateContacts}
                        onChange={(e) => handlePermissionChange(user.id, 'canCreateContacts', e.target.checked)}
                        disabled={updating === user.id}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-center">
                    {user.role === 'ADMIN' && user.email === '<EMAIL>' ? (
                      getPermissionIcon(user.canEditContacts)
                    ) : (
                      <input
                        type="checkbox"
                        checked={user.canEditContacts}
                        onChange={(e) => handlePermissionChange(user.id, 'canEditContacts', e.target.checked)}
                        disabled={updating === user.id}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-center">
                    {user.role === 'ADMIN' && user.email === '<EMAIL>' ? (
                      getPermissionIcon(user.canDeleteContacts)
                    ) : (
                      <input
                        type="checkbox"
                        checked={user.canDeleteContacts}
                        onChange={(e) => handlePermissionChange(user.id, 'canDeleteContacts', e.target.checked)}
                        disabled={updating === user.id}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-center">
                    {user.role === 'ADMIN' && user.email === '<EMAIL>' ? (
                      getPermissionIcon(user.canViewAllContacts)
                    ) : (
                      <input
                        type="checkbox"
                        checked={user.canViewAllContacts}
                        onChange={(e) => handlePermissionChange(user.id, 'canViewAllContacts', e.target.checked)}
                        disabled={updating === user.id}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-center">
                    {user.role === 'ADMIN' && user.email === '<EMAIL>' ? (
                      getPermissionIcon(user.isActive)
                    ) : (
                      <input
                        type="checkbox"
                        checked={user.isActive}
                        onChange={(e) => handlePermissionChange(user.id, 'isActive', e.target.checked)}
                        disabled={updating === user.id}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-center text-sm text-gray-900">
                    {user._count?.contacts || 0}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h3 className="text-lg font-medium text-blue-900 mb-2">Légende des permissions</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-blue-800">
          <div>
            <strong>Créer :</strong> Peut ajouter de nouveaux contacts
          </div>
          <div>
            <strong>Modifier :</strong> Peut modifier les contacts existants
          </div>
          <div>
            <strong>Supprimer :</strong> Peut supprimer des contacts
          </div>
          <div>
            <strong>Voir Tout :</strong> Peut voir les contacts de tous les utilisateurs
          </div>
        </div>
        <p className="text-xs text-blue-600 mt-2">
          Note : L'administrateur principal ne peut pas être modifié pour des raisons de sécurité.
        </p>
      </div>
    </div>
  );
};

export default UserPermissions;
