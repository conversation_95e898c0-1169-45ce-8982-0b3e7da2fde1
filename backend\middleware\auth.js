const { verifyToken, extractTokenFromHeader } = require('../utils/auth');
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

/**
 * Middleware d'authentification
 * Vérifie le token JWT et ajoute l'utilisateur à req.user
 */
const authenticateToken = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    const token = extractTokenFromHeader(authHeader);

    if (!token) {
      return res.status(401).json({
        error: 'Token d\'authentification requis',
        code: 'NO_TOKEN'
      });
    }

    // Vérifier le token
    const decoded = verifyToken(token);

    // Récupérer l'utilisateur depuis la base de données avec ses permissions
    const user = await prisma.user.findUnique({
      where: { id: decoded.userId },
      select: {
        id: true,
        email: true,
        nom: true,
        prenom: true,
        role: true,
        isActive: true,
        canCreateContacts: true,
        canEditContacts: true,
        canDeleteContacts: true,
        canViewAllContacts: true
      }
    });

    if (!user) {
      return res.status(401).json({
        error: 'Utilisateur non trouvé',
        code: 'USER_NOT_FOUND'
      });
    }

    if (!user.isActive) {
      return res.status(401).json({
        error: 'Compte utilisateur désactivé',
        code: 'USER_INACTIVE'
      });
    }

    // Ajouter l'utilisateur à la requête
    req.user = user;
    next();

  } catch (error) {
    console.error('Erreur d\'authentification:', error);

    if (error.message === 'Token invalide') {
      return res.status(401).json({
        error: 'Token invalide ou expiré',
        code: 'INVALID_TOKEN'
      });
    }

    return res.status(500).json({
      error: 'Erreur serveur lors de l\'authentification',
      code: 'AUTH_ERROR'
    });
  }
};

/**
 * Middleware de vérification des rôles
 * Vérifie que l'utilisateur a le rôle requis
 */
const requireRole = (requiredRole) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        error: 'Authentification requise',
        code: 'NO_AUTH'
      });
    }

    if (req.user.role !== requiredRole && req.user.role !== 'ADMIN') {
      return res.status(403).json({
        error: 'Permissions insuffisantes',
        code: 'INSUFFICIENT_PERMISSIONS',
        required: requiredRole,
        current: req.user.role
      });
    }

    next();
  };
};

/**
 * Middleware pour les administrateurs uniquement
 */
const requireAdmin = requireRole('ADMIN');

/**
 * Middleware optionnel d'authentification
 * Ajoute l'utilisateur à req.user s'il est connecté, sinon continue
 */
const optionalAuth = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    const token = extractTokenFromHeader(authHeader);

    if (!token) {
      req.user = null;
      return next();
    }

    const decoded = verifyToken(token);
    const user = await prisma.user.findUnique({
      where: { id: decoded.userId },
      select: {
        id: true,
        email: true,
        nom: true,
        prenom: true,
        role: true,
        isActive: true
      }
    });

    req.user = user && user.isActive ? user : null;
    next();

  } catch (error) {
    // En cas d'erreur, continuer sans utilisateur
    req.user = null;
    next();
  }
};

/**
 * Middleware de vérification de propriété
 * Vérifie que l'utilisateur peut accéder à la ressource
 */
const checkResourceOwnership = (resourceType) => {
  return async (req, res, next) => {
    try {
      const resourceId = parseInt(req.params.id);
      const userId = req.user.id;

      // Les admins peuvent accéder à tout
      if (req.user.role === 'ADMIN') {
        return next();
      }

      let resource;
      switch (resourceType) {
        case 'contact':
          resource = await prisma.contact.findUnique({
            where: { id: resourceId },
            select: { userId: true }
          });
          break;
        case 'user':
          // Un utilisateur ne peut accéder qu'à ses propres données
          if (resourceId !== userId) {
            return res.status(403).json({
              error: 'Accès non autorisé à cette ressource',
              code: 'RESOURCE_ACCESS_DENIED'
            });
          }
          return next();
        default:
          return res.status(400).json({
            error: 'Type de ressource non supporté',
            code: 'UNSUPPORTED_RESOURCE_TYPE'
          });
      }

      if (!resource) {
        return res.status(404).json({
          error: 'Ressource non trouvée',
          code: 'RESOURCE_NOT_FOUND'
        });
      }

      if (resource.userId !== userId) {
        return res.status(403).json({
          error: 'Accès non autorisé à cette ressource',
          code: 'RESOURCE_ACCESS_DENIED'
        });
      }

      next();

    } catch (error) {
      console.error('Erreur de vérification de propriété:', error);
      return res.status(500).json({
        error: 'Erreur serveur lors de la vérification des permissions',
        code: 'OWNERSHIP_CHECK_ERROR'
      });
    }
  };
};

module.exports = {
  authenticateToken,
  requireRole,
  requireAdmin,
  optionalAuth,
  checkResourceOwnership
};
