# Script PowerShell pour démarrer l'application Contact Manager

Write-Host "🚀 Démarrage de l'application Contact Manager..." -ForegroundColor Green

# Vérifier si Node.js est installé
try {
    $nodeVersion = node --version
    Write-Host "✅ Node.js détecté: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Node.js n'est pas installé ou n'est pas dans le PATH" -ForegroundColor Red
    Write-Host "Veuillez installer Node.js depuis https://nodejs.org/" -ForegroundColor Yellow
    exit 1
}

# Fonction pour installer les dépendances si nécessaire
function Install-Dependencies {
    param($Path, $Name)
    
    if (!(Test-Path "$Path/node_modules")) {
        Write-Host "📦 Installation des dépendances pour $Name..." -ForegroundColor Yellow
        Set-Location $Path
        npm install
        if ($LASTEXITCODE -ne 0) {
            Write-Host "❌ Erreur lors de l'installation des dépendances pour $Name" -ForegroundColor Red
            exit 1
        }
        Set-Location ..
    } else {
        Write-Host "✅ Dépendances déjà installées pour $Name" -ForegroundColor Green
    }
}

# Installer les dépendances
Install-Dependencies "backend" "Backend"
Install-Dependencies "frontend" "Frontend"

# Configurer la base de données
Write-Host "🗄️ Configuration de la base de données..." -ForegroundColor Yellow
Set-Location backend

if (!(Test-Path ".env")) {
    Copy-Item "../.env.example" ".env"
    Write-Host "✅ Fichier .env créé" -ForegroundColor Green
}

# Générer le client Prisma et créer la base de données
npx prisma generate
npx prisma db push

Set-Location ..

# Démarrer les serveurs
Write-Host "🌐 Démarrage des serveurs..." -ForegroundColor Green

# Démarrer le backend en arrière-plan
Write-Host "🔧 Démarrage du backend sur http://localhost:3001..." -ForegroundColor Cyan
Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd backend; npm run dev"

# Attendre un peu pour que le backend démarre
Start-Sleep -Seconds 3

# Démarrer le frontend
Write-Host "⚛️ Démarrage du frontend sur http://localhost:3000..." -ForegroundColor Cyan
Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd frontend; npm run dev"

Write-Host ""
Write-Host "🎉 Application démarrée avec succès!" -ForegroundColor Green
Write-Host "📱 Frontend: http://localhost:3000" -ForegroundColor Cyan
Write-Host "🔧 Backend: http://localhost:3001" -ForegroundColor Cyan
Write-Host ""
Write-Host "Pour arrêter l'application, fermez les fenêtres de terminal ouvertes." -ForegroundColor Yellow
