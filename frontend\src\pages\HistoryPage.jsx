import React from 'react';
import { useAuth, ProtectedRoute } from '../contexts/AuthContext';
import Navbar from '../components/layout/Navbar';
import ActivityLog from '../components/history/ActivityLog';

const HistoryPage = () => {
  const { user } = useAuth();

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-gray-50">
        <Navbar />
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900">
              Mon Historique d'Activité
            </h1>
            <p className="text-gray-600 mt-2">
              Consultez toutes vos actions et modifications
            </p>
          </div>
          
          <ActivityLog userId={user?.id} showUserInfo={false} />
        </div>
      </div>
    </ProtectedRoute>
  );
};

export default HistoryPage;
