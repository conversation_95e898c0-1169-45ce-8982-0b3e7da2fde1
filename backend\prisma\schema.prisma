// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

model Contact {
  id        Int      @id @default(autoincrement())
  nom       String
  email     String   @unique
  telephone String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("contacts")
}
