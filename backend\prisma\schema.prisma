// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

model User {
  id        Int      @id @default(autoincrement())
  email     String   @unique
  password  String
  nom       String
  prenom    String
  role      String   @default("USER") // ADMIN ou USER
  isActive  Boolean  @default(true)
  lastLogin DateTime?

  // Permissions granulaires
  canCreateContacts <PERSON>olean @default(true)
  canEditContacts   <PERSON>olean @default(true)
  canDeleteContacts <PERSON>olean @default(false)
  canViewAllContacts Boolean @default(false) // Admin peut voir tous les contacts

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  contacts     Contact[]
  activityLogs ActivityLog[]

  @@map("users")
}

model Contact {
  id        Int      @id @default(autoincrement())
  nom       String
  email     String   @unique
  telephone String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  userId       Int
  user         User          @relation(fields: [userId], references: [id], onDelete: Cascade)
  activityLogs ActivityLog[]

  @@map("contacts")
}

model ActivityLog {
  id          Int      @id @default(autoincrement())
  action      String   // CREATE, UPDATE, DELETE, LOGIN, LOGOUT, REGISTER
  entityType  String   // CONTACT, USER, AUTH
  entityId    Int?
  oldValues   String?  // JSON stringifié
  newValues   String?  // JSON stringifié
  ipAddress   String?
  userAgent   String?
  description String?
  createdAt   DateTime @default(now())

  // Relations
  userId    Int
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  contactId Int?
  contact   Contact? @relation(fields: [contactId], references: [id], onDelete: SetNull)

  @@map("activity_logs")
}

// Enums remplacés par des constantes String pour SQLite
// UserRole: "ADMIN" | "USER"
// ActionType: "CREATE" | "UPDATE" | "DELETE" | "LOGIN" | "LOGOUT" | "REGISTER"
// EntityType: "CONTACT" | "USER" | "AUTH"
