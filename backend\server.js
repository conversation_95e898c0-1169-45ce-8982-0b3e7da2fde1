const express = require('express');
const cors = require('cors');
const dotenv = require('dotenv');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const { PrismaClient } = require('@prisma/client');

// Configuration
dotenv.config();
const app = express();
const prisma = new PrismaClient();
const PORT = process.env.PORT || 3001;

// Middleware de sécurité
app.use(helmet());

// Rate limiting (désactivé temporairement pour les tests)
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 1000, // limite augmentée pour les tests
  message: {
    error: 'Trop de requêtes depuis cette IP, veuillez réessayer plus tard.'
  }
});

const authLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 50, // limite augmentée pour les tests
  message: {
    error: 'Trop de tentatives de connexion, veuillez réessayer plus tard.'
  }
});

app.use(limiter);
app.use('/auth/login', authLimiter);
app.use('/auth/register', authLimiter);

// Middleware de base
app.use(cors({
  origin: process.env.FRONTEND_URL || 'http://localhost:3000',
  credentials: true
}));
app.use(express.json({ limit: '10mb' }));

// Trust proxy pour obtenir la vraie IP derrière un reverse proxy
app.set('trust proxy', 1);

// Import des routes
const authRoutes = require('./routes/auth');
const contactRoutes = require('./routes/contacts');
const adminRoutes = require('./routes/admin');

// Routes
app.use('/auth', authRoutes);
app.use('/contacts', contactRoutes);
app.use('/admin', adminRoutes);


// Route de test
app.get('/', (req, res) => {
  res.json({ message: 'API de gestion de contacts - Serveur en fonctionnement' });
});

// Gestion des erreurs globales
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({ error: 'Erreur serveur interne' });
});

// Démarrage du serveur
app.listen(PORT, () => {
  console.log(`Serveur démarré sur le port ${PORT}`);
});

// Gestion propre de l'arrêt
process.on('SIGINT', async () => {
  await prisma.$disconnect();
  process.exit(0);
});
