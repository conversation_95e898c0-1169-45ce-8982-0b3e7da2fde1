const express = require('express');
const cors = require('cors');
const dotenv = require('dotenv');
const { PrismaClient } = require('@prisma/client');

// Configuration
dotenv.config();
const app = express();
const prisma = new PrismaClient();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(cors());
app.use(express.json());

// Routes pour les contacts

// GET /contacts - Récupérer tous les contacts avec recherche optionnelle
app.get('/contacts', async (req, res) => {
  try {
    const { search } = req.query;
    
    let contacts;
    if (search) {
      contacts = await prisma.contact.findMany({
        where: {
          OR: [
            { nom: { contains: search, mode: 'insensitive' } },
            { email: { contains: search, mode: 'insensitive' } }
          ]
        },
        orderBy: { nom: 'asc' }
      });
    } else {
      contacts = await prisma.contact.findMany({
        orderBy: { nom: 'asc' }
      });
    }
    
    res.json(contacts);
  } catch (error) {
    console.error('Erreur lors de la récupération des contacts:', error);
    res.status(500).json({ error: 'Erreur serveur lors de la récupération des contacts' });
  }
});

// GET /contacts/:id - Récupérer un contact par ID
app.get('/contacts/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const contact = await prisma.contact.findUnique({
      where: { id: parseInt(id) }
    });
    
    if (!contact) {
      return res.status(404).json({ error: 'Contact non trouvé' });
    }
    
    res.json(contact);
  } catch (error) {
    console.error('Erreur lors de la récupération du contact:', error);
    res.status(500).json({ error: 'Erreur serveur lors de la récupération du contact' });
  }
});

// POST /contacts - Créer un nouveau contact
app.post('/contacts', async (req, res) => {
  try {
    const { nom, email, telephone } = req.body;
    
    // Validation des données
    if (!nom || !email || !telephone) {
      return res.status(400).json({ 
        error: 'Tous les champs sont requis (nom, email, telephone)' 
      });
    }
    
    // Validation de l'email
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return res.status(400).json({ error: 'Format d\'email invalide' });
    }
    
    const contact = await prisma.contact.create({
      data: { nom, email, telephone }
    });
    
    res.status(201).json(contact);
  } catch (error) {
    console.error('Erreur lors de la création du contact:', error);
    
    // Gestion de l'erreur d'email unique
    if (error.code === 'P2002' && error.meta?.target?.includes('email')) {
      return res.status(400).json({ error: 'Cet email est déjà utilisé' });
    }
    
    res.status(500).json({ error: 'Erreur serveur lors de la création du contact' });
  }
});

// PUT /contacts/:id - Mettre à jour un contact
app.put('/contacts/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { nom, email, telephone } = req.body;
    
    // Validation des données
    if (!nom || !email || !telephone) {
      return res.status(400).json({ 
        error: 'Tous les champs sont requis (nom, email, telephone)' 
      });
    }
    
    // Validation de l'email
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return res.status(400).json({ error: 'Format d\'email invalide' });
    }
    
    const contact = await prisma.contact.update({
      where: { id: parseInt(id) },
      data: { nom, email, telephone }
    });
    
    res.json(contact);
  } catch (error) {
    console.error('Erreur lors de la mise à jour du contact:', error);
    
    // Gestion de l'erreur si le contact n'existe pas
    if (error.code === 'P2025') {
      return res.status(404).json({ error: 'Contact non trouvé' });
    }
    
    // Gestion de l'erreur d'email unique
    if (error.code === 'P2002' && error.meta?.target?.includes('email')) {
      return res.status(400).json({ error: 'Cet email est déjà utilisé' });
    }
    
    res.status(500).json({ error: 'Erreur serveur lors de la mise à jour du contact' });
  }
});

// DELETE /contacts/:id - Supprimer un contact
app.delete('/contacts/:id', async (req, res) => {
  try {
    const { id } = req.params;
    
    await prisma.contact.delete({
      where: { id: parseInt(id) }
    });
    
    res.status(204).send();
  } catch (error) {
    console.error('Erreur lors de la suppression du contact:', error);
    
    // Gestion de l'erreur si le contact n'existe pas
    if (error.code === 'P2025') {
      return res.status(404).json({ error: 'Contact non trouvé' });
    }
    
    res.status(500).json({ error: 'Erreur serveur lors de la suppression du contact' });
  }
});

// Route de test
app.get('/', (req, res) => {
  res.json({ message: 'API de gestion de contacts - Serveur en fonctionnement' });
});

// Gestion des erreurs globales
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({ error: 'Erreur serveur interne' });
});

// Démarrage du serveur
app.listen(PORT, () => {
  console.log(`Serveur démarré sur le port ${PORT}`);
});

// Gestion propre de l'arrêt
process.on('SIGINT', async () => {
  await prisma.$disconnect();
  process.exit(0);
});
