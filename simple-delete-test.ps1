# Test simple de suppression
Write-Host "Test de suppression de contact..." -ForegroundColor Yellow

# Connexion
$loginResponse = Invoke-RestMethod -Uri "http://localhost:3001/auth/login" -Method POST -Body '{"email":"<EMAIL>","password":"Admin123!"}' -ContentType "application/json"
$token = $loginResponse.token
Write-Host "Connecte en tant que: $($loginResponse.user.prenom)" -ForegroundColor Green

$headers = @{
    "Authorization" = "Bearer $token"
    "Content-Type" = "application/json"
}

# Creer un contact
$contactData = '{"nom":"Test Suppression","email":"<EMAIL>","telephone":"0123456789"}'
$createResponse = Invoke-RestMethod -Uri "http://localhost:3001/contacts" -Method POST -Body $contactData -Headers $headers
$contactId = $createResponse.id
Write-Host "Contact cree avec ID: $contactId" -ForegroundColor Green

# Supprimer le contact
Write-Host "Suppression du contact..." -ForegroundColor Yellow
Invoke-RestMethod -Uri "http://localhost:3001/contacts/$contactId" -Method DELETE -Headers $headers
Write-Host "Contact supprime!" -ForegroundColor Green

# Verifier les logs
$activityResponse = Invoke-RestMethod -Uri "http://localhost:3001/admin/activity?limit=3" -Method GET -Headers $headers
Write-Host "Dernieres activites:" -ForegroundColor Yellow
foreach ($activity in $activityResponse.activities) {
    Write-Host "  $($activity.action) - $($activity.description)" -ForegroundColor White
}
