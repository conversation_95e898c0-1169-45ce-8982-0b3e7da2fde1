// Script de test pour l'authentification et la journalisation
const axios = require('axios');

const API_BASE_URL = 'http://localhost:3001';

async function testAuthentication() {
  console.log('🔐 Test du système d\'authentification et de journalisation...\n');

  try {
    // 1. Test de connexion admin
    console.log('1. Test de connexion administrateur...');
    const loginResponse = await axios.post(`${API_BASE_URL}/auth/login`, {
      email: '<EMAIL>',
      password: 'Admin123!'
    });

    console.log('✅ Connexion réussie!');
    console.log(`   Utilisateur: ${loginResponse.data.user.prenom} ${loginResponse.data.user.nom}`);
    console.log(`   Rôle: ${loginResponse.data.user.role}`);
    console.log(`   Token reçu: ${loginResponse.data.token.substring(0, 20)}...`);

    const adminToken = loginResponse.data.token;

    // 2. Test de récupération du profil
    console.log('\n2. Test de récupération du profil...');
    const profileResponse = await axios.get(`${API_BASE_URL}/auth/me`, {
      headers: { Authorization: `Bearer ${adminToken}` }
    });

    console.log('✅ Profil récupéré!');
    console.log(`   Email: ${profileResponse.data.user.email}`);
    console.log(`   Contacts: ${profileResponse.data.user._count.contacts}`);
    console.log(`   Logs: ${profileResponse.data.user._count.activityLogs}`);

    // 3. Test de création d'un utilisateur normal
    console.log('\n3. Test de création d\'un utilisateur normal...');
    const newUserData = {
      email: '<EMAIL>',
      password: 'User123!',
      nom: 'Test',
      prenom: 'Utilisateur'
    };

    const registerResponse = await axios.post(`${API_BASE_URL}/auth/register`, newUserData);
    console.log('✅ Utilisateur créé!');
    console.log(`   ID: ${registerResponse.data.user.id}`);
    console.log(`   Email: ${registerResponse.data.user.email}`);

    const userToken = registerResponse.data.token;

    // 4. Test de création d'un contact avec l'utilisateur normal
    console.log('\n4. Test de création d\'un contact...');
    const contactData = {
      nom: 'Contact Test',
      email: '<EMAIL>',
      telephone: '0123456789'
    };

    const contactResponse = await axios.post(`${API_BASE_URL}/contacts`, contactData, {
      headers: { Authorization: `Bearer ${userToken}` }
    });

    console.log('✅ Contact créé!');
    console.log(`   ID: ${contactResponse.data.id}`);
    console.log(`   Nom: ${contactResponse.data.nom}`);

    const contactId = contactResponse.data.id;

    // 5. Test de modification du contact
    console.log('\n5. Test de modification du contact...');
    const updatedContactData = {
      nom: 'Contact Test Modifié',
      email: '<EMAIL>',
      telephone: '0987654321'
    };

    await axios.put(`${API_BASE_URL}/contacts/${contactId}`, updatedContactData, {
      headers: { Authorization: `Bearer ${userToken}` }
    });

    console.log('✅ Contact modifié!');

    // 6. Test de récupération de l'historique du contact
    console.log('\n6. Test de récupération de l\'historique du contact...');
    const historyResponse = await axios.get(`${API_BASE_URL}/contacts/${contactId}/history`, {
      headers: { Authorization: `Bearer ${userToken}` }
    });

    console.log('✅ Historique récupéré!');
    console.log(`   Nombre d'actions: ${historyResponse.data.length}`);
    historyResponse.data.forEach((log, index) => {
      console.log(`   ${index + 1}. ${log.action} - ${log.description} (${new Date(log.createdAt).toLocaleString('fr-FR')})`);
    });

    // 7. Test des fonctionnalités admin
    console.log('\n7. Test des fonctionnalités administrateur...');
    
    // Récupérer tous les utilisateurs
    const usersResponse = await axios.get(`${API_BASE_URL}/admin/users`, {
      headers: { Authorization: `Bearer ${adminToken}` }
    });

    console.log('✅ Liste des utilisateurs récupérée!');
    console.log(`   Nombre d'utilisateurs: ${usersResponse.data.users.length}`);
    usersResponse.data.users.forEach(user => {
      console.log(`   - ${user.prenom} ${user.nom} (${user.email}) - ${user.role}`);
    });

    // Récupérer l'historique global
    const globalHistoryResponse = await axios.get(`${API_BASE_URL}/admin/activity?limit=10`, {
      headers: { Authorization: `Bearer ${adminToken}` }
    });

    console.log('\n✅ Historique global récupéré!');
    console.log(`   Nombre d'actions récentes: ${globalHistoryResponse.data.activities.length}`);
    globalHistoryResponse.data.activities.slice(0, 5).forEach((log, index) => {
      console.log(`   ${index + 1}. ${log.action} ${log.entityType} par ${log.user.prenom} ${log.user.nom} (${new Date(log.createdAt).toLocaleString('fr-FR')})`);
    });

    // Récupérer les statistiques
    const statsResponse = await axios.get(`${API_BASE_URL}/admin/stats`, {
      headers: { Authorization: `Bearer ${adminToken}` }
    });

    console.log('\n✅ Statistiques récupérées!');
    console.log(`   Total utilisateurs: ${statsResponse.data.users.total}`);
    console.log(`   Admins: ${statsResponse.data.users.byRole.ADMIN || 0}`);
    console.log(`   Users: ${statsResponse.data.users.byRole.USER || 0}`);
    console.log(`   Total contacts: ${statsResponse.data.contacts.total}`);
    console.log(`   Total actions: ${statsResponse.data.activity.totalActions}`);

    // 8. Test de déconnexion
    console.log('\n8. Test de déconnexion...');
    await axios.post(`${API_BASE_URL}/auth/logout`, {}, {
      headers: { Authorization: `Bearer ${userToken}` }
    });

    console.log('✅ Déconnexion réussie!');

    console.log('\n🎉 Tous les tests d\'authentification sont passés avec succès!');
    console.log('\n📊 Résumé des fonctionnalités testées:');
    console.log('   ✅ Connexion/Déconnexion');
    console.log('   ✅ Création d\'utilisateurs');
    console.log('   ✅ Gestion des contacts avec authentification');
    console.log('   ✅ Journalisation des actions');
    console.log('   ✅ Historique des contacts');
    console.log('   ✅ Administration des utilisateurs');
    console.log('   ✅ Historique global');
    console.log('   ✅ Statistiques');

    console.log('\n🔐 Identifiants de test créés:');
    console.log('   Admin: <EMAIL> / Admin123!');
    console.log('   User: <EMAIL> / User123!');

  } catch (error) {
    console.error('\n❌ Erreur lors du test:', error.message);
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', error.response.data);
    }
    console.log('\n💡 Assurez-vous que le serveur backend est démarré sur le port 3001');
  }
}

// Vérifier si axios est disponible
try {
  require('axios');
  testAuthentication();
} catch (error) {
  console.log('❌ Axios n\'est pas installé.');
  console.log('Installez-le avec: npm install axios');
}
