const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

// Utilisateurs avec leurs permissions
const sampleUsers = [
  {
    email: "<EMAIL>",
    password: "Admin123!",
    nom: "Administrateur",
    prenom: "Syst<PERSON>",
    role: "ADMIN",
    canCreateContacts: true,
    canEditContacts: true,
    canDeleteContacts: true,
    canViewAllContacts: true,
    contacts: [
      {
        nom: "<PERSON>",
        email: "<EMAIL>",
        telephone: "0123456789"
      },
      {
        nom: "<PERSON>",
        email: "<EMAIL>",
        telephone: "0234567890"
      }
    ]
  },
  {
    email: "<EMAIL>",
    password: "User123!",
    nom: "Test",
    prenom: "Utilisateur",
    role: "USER",
    canCreateContacts: true,
    canEditContacts: true,
    canDeleteContacts: false, // Pas de droit de suppression
    canViewAllContacts: false,
    contacts: [
      {
        nom: "<PERSON>",
        email: "<EMAIL>",
        telephone: "0345678901"
      },
      {
        nom: "<PERSON>",
        email: "<EMAIL>",
        telephone: "0456789012"
      }
    ]
  },
  {
    email: "<EMAIL>",
    password: "Manager123!",
    nom: "Manager",
    prenom: "Équipe",
    role: "USER",
    canCreateContacts: true,
    canEditContacts: true,
    canDeleteContacts: true, // Droits complets
    canViewAllContacts: true, // Peut voir tous les contacts
    contacts: [
      {
        nom: "Thomas Bernard",
        email: "<EMAIL>",
        telephone: "0567890123"
      },
      {
        nom: "Camille Petit",
        email: "<EMAIL>",
        telephone: "0678901234"
      }
    ]
  },
  {
    email: "<EMAIL>",
    password: "ReadOnly123!",
    nom: "Lecture",
    prenom: "Seule",
    role: "USER",
    canCreateContacts: false, // Pas de création
    canEditContacts: false,   // Pas de modification
    canDeleteContacts: false, // Pas de suppression
    canViewAllContacts: false,
    contacts: [
      {
        nom: "Lucas Moreau",
        email: "<EMAIL>",
        telephone: "0789012345"
      },
      {
        nom: "Emma Fournier",
        email: "<EMAIL>",
        telephone: "0890123456"
      }
    ]
  }
];

async function main() {
  console.log('🌱 Début du seeding de la base de données...');

  // Supprimer toutes les données existantes
  await prisma.activityLog.deleteMany();
  await prisma.contact.deleteMany();
  await prisma.user.deleteMany();
  console.log('🗑️ Données existantes supprimées');

  // Créer les utilisateurs avec leurs contacts et permissions
  for (const userData of sampleUsers) {
    const { contacts, password, ...userInfo } = userData;

    // Hasher le mot de passe
    const hashedPassword = await bcrypt.hash(password, 10);

    // Créer l'utilisateur avec ses contacts
    const createdUser = await prisma.user.create({
      data: {
        ...userInfo,
        password: hashedPassword,
        contacts: {
          create: contacts
        }
      },
      include: {
        contacts: true
      }
    });

    console.log(`✅ Utilisateur créé: ${createdUser.prenom} ${createdUser.nom} (${createdUser.email})`);
    console.log(`   - Rôle: ${createdUser.role}`);
    console.log(`   - Permissions: Créer=${createdUser.canCreateContacts}, Modifier=${createdUser.canEditContacts}, Supprimer=${createdUser.canDeleteContacts}, Voir tout=${createdUser.canViewAllContacts}`);
    console.log(`   - Contacts: ${createdUser.contacts.length}`);
  }

  const totalUsers = await prisma.user.count();
  const totalContacts = await prisma.contact.count();

  console.log(`🎉 Seeding terminé!`);
  console.log(`   - ${totalUsers} utilisateurs créés`);
  console.log(`   - ${totalContacts} contacts créés`);
  console.log(`   - Système de permissions granulaires activé`);
}

main()
  .catch((e) => {
    console.error('❌ Erreur lors du seeding:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
