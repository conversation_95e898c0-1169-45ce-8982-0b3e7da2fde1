import React from 'react';
import { useAuth, ProtectedRoute } from '../contexts/AuthContext';
import Navbar from '../components/layout/Navbar';
import AdminDashboard from '../components/admin/AdminDashboard';

const AdminPage = () => {
  return (
    <ProtectedRoute requireAdmin={true}>
      <div className="min-h-screen bg-gray-50">
        <Navbar />
        <AdminDashboard />
      </div>
    </ProtectedRoute>
  );
};

export default AdminPage;
