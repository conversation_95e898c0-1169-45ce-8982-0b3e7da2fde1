import React, { useState, useEffect } from 'react';
import { authenticatedAPI } from '../../services/authService';

const UserPermissionsSimple = () => {
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchUsers();
  }, []);

  const fetchUsers = async () => {
    try {
      setLoading(true);
      console.log('Fetching users...');
      const response = await authenticatedAPI.get('/admin/users');
      console.log('Users response:', response.data);
      setUsers(response.data.users || []);
    } catch (error) {
      console.error('Erreur lors de la récupération des utilisateurs:', error);
      setError('Erreur lors de la récupération des utilisateurs: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const updateUserPermissions = async (userId, permission, value) => {
    try {
      console.log(`Updating user ${userId}: ${permission} = ${value}`);
      const user = users.find(u => u.id === userId);
      if (!user) return;

      const newPermissions = {
        canCreateContacts: user.canCreateContacts,
        canEditContacts: user.canEditContacts,
        canDeleteContacts: user.canDeleteContacts,
        canViewAllContacts: user.canViewAllContacts,
        isActive: user.isActive,
        [permission]: value
      };

      const response = await authenticatedAPI.put(`/admin/users/${userId}/permissions`, newPermissions);
      console.log('Update response:', response.data);
      
      // Mettre à jour l'utilisateur dans la liste
      setUsers(users.map(u => 
        u.id === userId ? { ...u, ...response.data.user } : u
      ));
      
      alert('Permissions mises à jour avec succès !');
    } catch (error) {
      console.error('Erreur lors de la mise à jour des permissions:', error);
      alert('Erreur lors de la mise à jour des permissions: ' + error.message);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2">Chargement des utilisateurs...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-4">
        <p className="text-red-800">{error}</p>
        <button 
          onClick={fetchUsers}
          className="mt-2 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
        >
          Réessayer
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-gray-900">🔐 Gestion des Permissions</h2>
        <button 
          onClick={fetchUsers}
          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          Actualiser
        </button>
      </div>

      <div className="bg-white shadow-lg rounded-lg overflow-hidden">
        <div className="p-4 bg-gray-50 border-b">
          <h3 className="text-lg font-medium text-gray-900">
            Utilisateurs ({users.length})
          </h3>
        </div>
        
        <div className="divide-y divide-gray-200">
          {users.map((user) => (
            <div key={user.id} className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="text-lg font-medium text-gray-900">
                    {user.prenom} {user.nom}
                  </h4>
                  <p className="text-sm text-gray-500">{user.email}</p>
                  <span className={`inline-block px-2 py-1 rounded-full text-xs font-medium ${
                    user.role === 'ADMIN' 
                      ? 'bg-purple-100 text-purple-800' 
                      : 'bg-blue-100 text-blue-800'
                  }`}>
                    {user.role}
                  </span>
                </div>
                
                <div className="space-y-2">
                  <div className="flex items-center space-x-4">
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={user.canCreateContacts}
                        onChange={(e) => updateUserPermissions(user.id, 'canCreateContacts', e.target.checked)}
                        disabled={user.role === 'ADMIN' && user.email === '<EMAIL>'}
                        className="mr-2"
                      />
                      Créer
                    </label>
                    
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={user.canEditContacts}
                        onChange={(e) => updateUserPermissions(user.id, 'canEditContacts', e.target.checked)}
                        disabled={user.role === 'ADMIN' && user.email === '<EMAIL>'}
                        className="mr-2"
                      />
                      Modifier
                    </label>
                    
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={user.canDeleteContacts}
                        onChange={(e) => updateUserPermissions(user.id, 'canDeleteContacts', e.target.checked)}
                        disabled={user.role === 'ADMIN' && user.email === '<EMAIL>'}
                        className="mr-2"
                      />
                      Supprimer
                    </label>
                    
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={user.canViewAllContacts}
                        onChange={(e) => updateUserPermissions(user.id, 'canViewAllContacts', e.target.checked)}
                        disabled={user.role === 'ADMIN' && user.email === '<EMAIL>'}
                        className="mr-2"
                      />
                      Voir tout
                    </label>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h3 className="text-lg font-medium text-blue-900 mb-2">💡 Guide d'utilisation</h3>
        <div className="text-sm text-blue-800 space-y-1">
          <p><strong>Créer :</strong> Permet d'ajouter de nouveaux contacts</p>
          <p><strong>Modifier :</strong> Permet de modifier les contacts existants</p>
          <p><strong>Supprimer :</strong> Permet de supprimer des contacts</p>
          <p><strong>Voir tout :</strong> Permet de voir les contacts de tous les utilisateurs</p>
        </div>
        <p className="text-xs text-blue-600 mt-2">
          Note : L'administrateur principal ne peut pas être modifié pour des raisons de sécurité.
        </p>
      </div>
    </div>
  );
};

export default UserPermissionsSimple;
