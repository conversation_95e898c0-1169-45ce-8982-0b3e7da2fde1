import React from 'react';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import AuthPage from './components/auth/AuthPage';

// Composant simple pour tester
function ContactManager() {
  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-900 mb-4">
          🎉 Application de Contacts
        </h1>
        <p className="text-gray-600 mb-4">
          Vous êtes connecté avec succès !
        </p>
        <div className="bg-white p-6 rounded-lg shadow-lg max-w-md mx-auto">
          <h2 className="text-xl font-semibold text-gray-800 mb-4">
            Fonctionnalités disponibles :
          </h2>
          <ul className="text-left space-y-2 text-gray-600">
            <li>✅ Authentification fonctionnelle</li>
            <li>✅ Interface utilisateur</li>
            <li>✅ Base de données connectée</li>
            <li>✅ API backend opérationnelle</li>
          </ul>
          <button 
            onClick={() => window.location.reload()}
            className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Actualiser
          </button>
        </div>
      </div>
    </div>
  );
}

// Composant qui gère l'affichage selon l'état d'authentification
function AppContent() {
  const { isAuthenticated, loading } = useAuth();

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Chargement...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return <AuthPage />;
  }

  return <ContactManager />;
}

// Composant App principal avec gestion de l'authentification
function App() {
  return (
    <AuthProvider>
      <AppContent />
    </AuthProvider>
  );
}

export default App;
