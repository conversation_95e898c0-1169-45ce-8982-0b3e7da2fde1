import React from 'react';

// Version ultra-minimale pour tester
function App() {
  return (
    <div style={{
      minHeight: '100vh',
      backgroundColor: '#f0f9ff',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      fontFamily: 'Arial, sans-serif'
    }}>
      <div style={{
        backgroundColor: 'white',
        padding: '2rem',
        borderRadius: '8px',
        boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
        textAlign: 'center',
        maxWidth: '400px'
      }}>
        <h1 style={{
          color: '#1e40af',
          marginBottom: '1rem',
          fontSize: '2rem'
        }}>
          🎉 Application Test
        </h1>
        <p style={{
          color: '#6b7280',
          marginBottom: '1.5rem'
        }}>
          Si vous voyez cette page, React fonctionne !
        </p>
        <div style={{
          backgroundColor: '#dcfce7',
          border: '1px solid #16a34a',
          borderRadius: '4px',
          padding: '1rem',
          marginBottom: '1rem'
        }}>
          <h3 style={{ color: '#15803d', margin: '0 0 0.5rem 0' }}>
            ✅ État du système :
          </h3>
          <ul style={{ 
            textAlign: 'left', 
            margin: 0, 
            paddingLeft: '1.5rem',
            color: '#166534'
          }}>
            <li>React : Fonctionnel</li>
            <li>JavaScript : Opérationnel</li>
            <li>CSS : Appliqué</li>
            <li>Rendu : Réussi</li>
          </ul>
        </div>
        <button 
          onClick={() => {
            alert('JavaScript fonctionne !');
            window.location.reload();
          }}
          style={{
            backgroundColor: '#2563eb',
            color: 'white',
            border: 'none',
            padding: '0.75rem 1.5rem',
            borderRadius: '4px',
            cursor: 'pointer',
            fontSize: '1rem',
            fontWeight: '500'
          }}
        >
          🔄 Test JavaScript
        </button>
      </div>
    </div>
  );
}

export default App;
