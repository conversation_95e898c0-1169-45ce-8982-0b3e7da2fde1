import React from 'react';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import AuthPage from './components/auth/AuthPage';

// Version de test simple
function TestApp() {
  const { isAuthenticated, loading, user } = useAuth();

  console.log('TestApp - État:', { isAuthenticated, loading, user });

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-blue-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-blue-600 font-medium">Chargement de l'application...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
        <div className="container mx-auto px-4 py-8">
          <div className="text-center mb-8">
            <h1 className="text-4xl font-bold text-gray-900 mb-2">
              🔧 Mode Test - Connexion
            </h1>
            <p className="text-gray-600">
              Page de connexion en mode test
            </p>
          </div>
          <AuthPage />
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-green-50">
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <h1 className="text-4xl font-bold text-green-800 mb-4">
            ✅ Application Test - Connecté !
          </h1>
          <div className="bg-white p-6 rounded-lg shadow-lg max-w-md mx-auto">
            <h2 className="text-xl font-semibold text-gray-800 mb-4">
              Informations utilisateur :
            </h2>
            <div className="text-left space-y-2">
              <p><strong>Nom :</strong> {user?.prenom} {user?.nom}</p>
              <p><strong>Email :</strong> {user?.email}</p>
              <p><strong>Rôle :</strong> {user?.role}</p>
            </div>
            <div className="mt-6 space-y-2">
              <button 
                onClick={() => window.location.hash = 'admin'}
                className="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                🔐 Test Administration
              </button>
              <button 
                onClick={() => window.location.reload()}
                className="w-full px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
              >
                🔄 Actualiser
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

function App() {
  return (
    <AuthProvider>
      <TestApp />
    </AuthProvider>
  );
}

export default App;
